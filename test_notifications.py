#!/usr/bin/env python
"""
Test script for notification integrations.

This script can be run to test various notification scenarios.
Run with: python manage.py shell < test_notifications.py
"""

import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bowenmfb.settings')
django.setup()

from django.contrib.auth.models import User
from account.models import Customer, Company, CompanyAccount
from transfer.models import SingleTransferRequest, BulkTransferRequest
from billpayment.models import BillPaymentRequest, Bill<PERSON>, BillerCategory
from superadmin.models import Admin<PERSON>ser, AdminRole, AccountCreationRequest
from bowenmfb.modules.utils import send_notification_async, log_request


def test_direct_notifications():
    """Test direct notification sending."""
    print("=== Testing Direct Notifications ===")
    
    # Test SMS
    print("Testing SMS notification...")
    sms_task = send_notification_async(
        notification_type='sms',
        recipient='***********',
        message='Test SMS from notification system'
    )
    print(f"SMS task queued: {sms_task.id}")
    
    # Test Email
    print("Testing email notification...")
    email_task = send_notification_async(
        notification_type='email',
        recipient='<EMAIL>',
        message='This is a test email from the notification system.',
        subject='Test Notification'
    )
    print(f"Email task queued: {email_task.id}")


def test_transfer_notifications():
    """Test transfer status change notifications."""
    print("\n=== Testing Transfer Notifications ===")
    
    try:
        # Find an existing transfer request or create a test scenario
        transfer = SingleTransferRequest.objects.first()
        if transfer:
            print(f"Testing with transfer ID: {transfer.id}")
            
            # Simulate status changes
            if not transfer.is_checked:
                print("Setting transfer as checked...")
                transfer.is_checked = True
                transfer.save()
                print("Transfer checked notification should be sent")
            
            if not transfer.is_verified:
                print("Setting transfer as verified...")
                transfer.is_verified = True
                transfer.save()
                print("Transfer verified notification should be sent")
        else:
            print("No transfer requests found to test with")
            
    except Exception as e:
        print(f"Error testing transfer notifications: {e}")


def test_bill_payment_notifications():
    """Test bill payment status change notifications."""
    print("\n=== Testing Bill Payment Notifications ===")
    
    try:
        # Find an existing bill payment request
        bill_payment = BillPaymentRequest.objects.first()
        if bill_payment:
            print(f"Testing with bill payment ID: {bill_payment.id}")
            
            # Simulate status changes
            if not bill_payment.is_checked:
                print("Setting bill payment as checked...")
                bill_payment.is_checked = True
                bill_payment.save()
                print("Bill payment checked notification should be sent")
        else:
            print("No bill payment requests found to test with")
            
    except Exception as e:
        print(f"Error testing bill payment notifications: {e}")


def test_admin_notifications():
    """Test admin-related notifications."""
    print("\n=== Testing Admin Notifications ===")
    
    try:
        # Test account creation request notification
        account_request = AccountCreationRequest.objects.first()
        if account_request:
            print(f"Found account request ID: {account_request.id}")
            print("Admin notification should have been sent when this was created")
        else:
            print("No account creation requests found")
            
        # Test admin user count
        admin_count = AdminUser.objects.filter(is_active=True).count()
        print(f"Active admin users who would receive notifications: {admin_count}")
        
    except Exception as e:
        print(f"Error testing admin notifications: {e}")


def test_user_registration_flow():
    """Test user registration notification flow."""
    print("\n=== Testing User Registration Flow ===")
    
    try:
        # This would normally be tested through the API endpoints
        print("User registration notifications are tested through:")
        print("1. POST /api/account/signup - SignUpSerializerIn")
        print("2. POST /api/account/request-verification - RequestVerificationLinkSerializerIn")
        print("3. POST /api/account/create-additional-account - CreateAdditionalAccountSerializerIn")
        
        # Check if we have test data
        customer_count = Customer.objects.count()
        company_count = Company.objects.count()
        account_count = CompanyAccount.objects.count()
        
        print(f"Current data: {customer_count} customers, {company_count} companies, {account_count} accounts")
        
    except Exception as e:
        print(f"Error in user registration flow test: {e}")


def check_notification_setup():
    """Check if notification system is properly set up."""
    print("\n=== Checking Notification Setup ===")
    
    try:
        from django.conf import settings
        
        # Check DEBUG setting
        print(f"DEBUG mode: {settings.DEBUG}")
        if settings.DEBUG:
            print("Will use tmsaas functions for notifications")
            # Check tmsaas settings
            tmsaas_settings = [
                'TMSAAS_BASE_URL',
                'TMSAAS_CLIENT_ID', 
                'TMSAAS_CLIENT_SECRET',
                'TMSAAS_SENDER_ID'
            ]
            for setting in tmsaas_settings:
                value = getattr(settings, setting, 'NOT SET')
                print(f"  {setting}: {'SET' if value != 'NOT SET' else 'NOT SET'}")
        else:
            print("Will use BankOne API for notifications")
            
        # Check Celery
        try:
            from celery import current_app
            print(f"Celery app: {current_app.main}")
            print("Celery is configured")
        except Exception as e:
            print(f"Celery configuration issue: {e}")
            
        # Check if signals are imported
        print("\nChecking signal imports:")
        try:
            import transfer.signals
            print("✓ Transfer signals imported")
        except ImportError as e:
            print(f"✗ Transfer signals import error: {e}")
            
        try:
            import billpayment.signals
            print("✓ Bill payment signals imported")
        except ImportError as e:
            print(f"✗ Bill payment signals import error: {e}")
            
        try:
            import superadmin.signals
            print("✓ Superadmin signals imported")
        except ImportError as e:
            print(f"✗ Superadmin signals import error: {e}")
            
    except Exception as e:
        print(f"Error checking notification setup: {e}")


def main():
    """Run all notification tests."""
    print("Starting Notification System Tests")
    print("=" * 50)
    
    check_notification_setup()
    test_direct_notifications()
    test_transfer_notifications()
    test_bill_payment_notifications()
    test_admin_notifications()
    test_user_registration_flow()
    
    print("\n" + "=" * 50)
    print("Notification tests completed!")
    print("\nTo monitor notifications:")
    print("1. Check Celery worker logs")
    print("2. Check application logs for notification attempts")
    print("3. Monitor tmsaas/BankOne API responses")


if __name__ == "__main__":
    main()
