# Signatory System Implementation Guide

## Overview

The signatory system has been implemented to manage transfer approvals for banking solutions. It supports both single-person accounts and multi-signatory accounts with configurable approval hierarchies.

## Key Features

### 1. **Flexible Signatory Hierarchy**
- Support for 1-5 approval levels
- Configurable roles: Uploader → Checker → Verifier → Approver
- Single signatory accounts for individual management
- Multi-signatory accounts for corporate governance

### 2. **Role-Based Permissions**
- **Uploader (Level 1)**: Can create transfer requests
- **Checker (Level 2)**: Can review and check transfers
- **Verifier (Level 3)**: Can verify checked transfers
- **Approver (Level 4-5)**: Can approve verified transfers

### 3. **Transfer Types Supported**
- **Single Transfers**: Individual transfer requests
- **Bulk Transfers**: CSV-based batch transfers

## Database Models

### AccountSignatory
Links customers to company accounts with their hierarchy level and permissions.

**Key Fields:**
- `company_account`: The account they can manage
- `customer`: The user with signatory rights
- `role`: Their role (uploader, checker, verifier, approver)
- `hierarchy_level`: Numeric level (1-5)
- `can_upload/can_check/can_verify/can_approve`: Permission flags

### SignatoryHierarchy
Defines the approval workflow for each company account.

**Key Fields:**
- `company_account`: The account this hierarchy applies to
- `total_levels`: Number of approval levels required (1-5)
- `is_single_signatory`: Whether account is managed by one person
- `requires_checker/verifier/approver`: Workflow requirements

### TransferApprovalWorkflow
Tracks the approval process for each transfer request.

**Key Fields:**
- `single_transfer_request` or `bulk_transfer_request`: The transfer being processed
- `current_level`: Current approval level
- `status`: pending, in_progress, approved, declined, cancelled
- Tracking fields for each approval stage with timestamps

## API Endpoints

### 1. Account Signatories Management
```
GET /transactions/signatories?account_id=<uuid>
POST /transactions/signatories
```

**Example POST Request:**
```json
{
    "company_account": "account-uuid",
    "customer": "customer-uuid", 
    "role": "checker",
    "hierarchy_level": 2,
    "can_check": true
}
```

### 2. Signatory Hierarchy Configuration
```
GET /transactions/signatory-hierarchy?account_id=<uuid>
POST /transactions/signatory-hierarchy
```

**Example POST Request:**
```json
{
    "account_id": "account-uuid",
    "total_levels": 3,
    "requires_checker": true,
    "requires_verifier": true,
    "requires_approver": true,
    "is_single_signatory": false
}
```

### 3. Transfer Workflow Management
```
GET /transactions/workflow?transfer_type=single&transfer_id=<uuid>
POST /transactions/workflow/process
```

**Example Process Request:**
```json
{
    "workflow_id": "workflow-uuid",
    "action": "approve",
    "remarks": "Transfer approved after verification"
}
```

### 4. Bulk Transfer Template
```
GET /transactions/bulk-template
```
Downloads the CSV template for bulk transfers.

## CSV Template Format

The bulk transfer template includes the following columns:

```csv
beneficiary_name,beneficiary_account_number,beneficiary_bank_code,bank_name,amount,narration,transfer_type
John Doe,**********,044,Access Bank,50000.00,Salary Payment,inter
Jane Smith,**********,,Bowen MFB,25000.00,Vendor Payment,intra
```

**Column Descriptions:**
- `beneficiary_name`: Full name of the recipient
- `beneficiary_account_number`: 10-digit account number
- `beneficiary_bank_code`: Bank code (empty for intra-bank transfers)
- `bank_name`: Name of the recipient's bank
- `amount`: Transfer amount in naira (decimal format)
- `narration`: Transfer description/purpose
- `transfer_type`: 'inter' for inter-bank, 'intra' for intra-bank

## Implementation Scenarios

### Scenario 1: Single Individual Account
```python
# Create hierarchy for single signatory
hierarchy = SignatoryHierarchy.objects.create(
    company_account=account,
    total_levels=1,
    is_single_signatory=True,
    requires_approver=True
)

# Add the single signatory
signatory = AccountSignatory.objects.create(
    company_account=account,
    customer=customer,
    role="approver",
    hierarchy_level=1,
    can_upload=True,
    can_approve=True
)
```

### Scenario 2: Multi-Signatory Account (5 levels)
```python
# Create hierarchy for 5-level approval
hierarchy = SignatoryHierarchy.objects.create(
    company_account=account,
    total_levels=5,
    is_single_signatory=False,
    requires_checker=True,
    requires_verifier=True,
    requires_approver=True
)

# Add signatories for each level
signatories = [
    {"customer": uploader, "role": "uploader", "level": 1, "can_upload": True},
    {"customer": checker1, "role": "checker", "level": 2, "can_check": True},
    {"customer": checker2, "role": "checker", "level": 2, "can_check": True},
    {"customer": verifier, "role": "verifier", "level": 3, "can_verify": True},
    {"customer": approver, "role": "approver", "level": 4, "can_approve": True},
]

for sig_data in signatories:
    AccountSignatory.objects.create(
        company_account=account,
        customer=sig_data["customer"],
        role=sig_data["role"],
        hierarchy_level=sig_data["level"],
        **{k: v for k, v in sig_data.items() if k.startswith("can_")}
    )
```

## Workflow Process

### Transfer Creation
1. User creates a transfer request (single or bulk)
2. System automatically creates a `TransferApprovalWorkflow`
3. Workflow starts at level 1 with status "pending"

### Approval Process
1. **Check** (Level 2): Checker reviews and validates the transfer
2. **Verify** (Level 3): Verifier confirms the transfer details
3. **Approve** (Level 4+): Approver gives final authorization
4. **Execute**: System processes the approved transfer

### Decline Process
- Any signatory can decline a transfer at any level
- Decline reason must be provided
- Transfer status becomes "declined"

## Admin Interface

All signatory models are available in Django Admin with comprehensive filtering and search capabilities:

- **Account Signatories**: Manage user permissions per account
- **Signatory Hierarchies**: Configure approval workflows
- **Transfer Approval Workflows**: Monitor transfer approval status

## Security Considerations

1. **Permission Validation**: System validates user permissions before allowing actions
2. **Audit Trail**: Complete tracking of who performed each action and when
3. **Level Enforcement**: Users can only perform actions at their authorized level
4. **Company Isolation**: Users can only manage transfers for their own company

## Next Steps

1. **Run Migrations**: Apply the database changes
2. **Configure Hierarchies**: Set up signatory hierarchies for each account
3. **Add Signatories**: Assign users to appropriate roles and levels
4. **Test Workflows**: Verify the approval process works as expected
5. **Train Users**: Educate users on the new approval process

## Migration Command

```bash
python manage.py migrate transfer
```

This will create the new signatory tables and relationships in your database.
