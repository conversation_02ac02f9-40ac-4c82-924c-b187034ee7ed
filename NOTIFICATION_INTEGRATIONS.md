# Notification Integrations

This document describes all the notification integrations implemented using the `send_notification_async()` function across the Bowen MFB system.

## Overview

The notification system automatically sends SMS and email notifications for various user actions and system events. It uses the `send_notification_async()` function which intelligently chooses between tmsaas (DEBUG mode) and BankOne API (production mode).

## Implemented Notifications

### 1. User Registration (SignUpSerializerIn)

**Trigger**: When a new user account is created
**Recipients**: The newly registered user
**Notifications Sent**:
- **SMS**: Welcome message with verification code
- **Email**: Welcome email with verification code and instructions

**Message Content**:
- SMS: "Welcome to Bowen MFB! Your account has been created successfully. Your verification code is: {code}"
- Email: Detailed welcome message with verification code and expiry information

### 2. Verification Code Request (RequestVerificationLinkSerializerIn)

**Trigger**: When a user requests a new verification code
**Recipients**: The requesting user
**Notifications Sent**:
- **SMS**: Verification code message
- **Email**: Verification code with instructions

**Message Content**:
- SMS: "Your Bowen MFB verification code is: {code}. This code will expire in 15 minutes."
- Email: Detailed verification message with code and security information

### 3. New Company Account Creation (CreateAdditionalAccountSerializerIn)

**Trigger**: When a new company account is successfully created
**Recipients**: All active customers of the company
**Notifications Sent**:
- **SMS**: New account notification with account number
- **Email**: Detailed account information including account officer details

**Message Content**:
- SMS: "New account created for {company}. Account Number: {account_no}. You can now use this account for transactions."
- Email: Complete account details with account officer information

### 4. Transfer Request Status Changes

**Trigger**: When transfer request status changes (checked, verified, approved, declined)
**Recipients**: The customer who created the transfer request
**Models**: SingleTransferRequest, BulkTransferRequest

#### Single Transfer Notifications:
- **Checked**: "Your transfer request has been checked and is under review"
- **Verified**: "Your transfer request has been verified and is awaiting final approval"
- **Approved**: "Great news! Your transfer request has been approved and will be processed"
- **Declined**: "Your transfer request has been declined" (with reason)

#### Bulk Transfer Notifications:
- **Recipients**: All active customers of the company
- **Content**: Similar to single transfers but mentions bulk processing

### 5. Bill Payment Request Status Changes

**Trigger**: When bill payment request status changes (checked, verified, approved, declined)
**Recipients**: The customer who created the bill payment request
**Model**: BillPaymentRequest

**Status Notifications**:
- **Checked**: "Your bill payment has been checked and is under review"
- **Verified**: "Your bill payment has been verified and is awaiting final approval"
- **Approved**: "Great news! Your bill payment has been approved and will be processed"
- **Declined**: "Your bill payment has been declined" (with reason)

### 6. Account Creation Request Notifications

**Trigger**: When a new account creation request is submitted
**Recipients**: All active admin users
**Model**: AccountCreationRequest

**Message Content**:
- **SMS**: "New account creation request from {business_name}. Please review and take action."
- **Email**: Detailed company information and request details for admin review

### 7. Admin User Creation

**Trigger**: When a new admin user is created
**Recipients**: The newly created admin user
**Model**: AdminUser

**Message Content**:
- **SMS**: Welcome message with role information
- **Email**: Detailed welcome message with account details, role, and permissions

## Technical Implementation

### Signal-Based Notifications

The system uses Django signals to automatically trigger notifications:

1. **transfer/signals.py**: Handles transfer request status change notifications
2. **billpayment/signals.py**: Handles bill payment request status change notifications
3. **superadmin/signals.py**: Handles admin-related notifications

### Serializer-Based Notifications

Direct integration in serializers for immediate actions:

1. **account/serializers.py**: 
   - SignUpSerializerIn: Welcome and verification notifications
   - RequestVerificationLinkSerializerIn: Verification code notifications
   - CreateAdditionalAccountSerializerIn: New account notifications

### Status Change Detection

For transfer and bill payment requests, the system uses `pre_save` and `post_save` signals:

1. **pre_save**: Captures the old state of status fields
2. **post_save**: Compares old and new states to detect changes
3. **transaction.on_commit**: Ensures notifications are sent after successful database commits

## Configuration

### Environment-Based Behavior

- **DEBUG = True**: Uses tmsaas functions for faster development testing
- **DEBUG = False**: Uses BankOne API for production messaging

### Default Settings

```python
# Default SMS account number
DEFAULT_SMS_ACCOUNT_NUMBER = '**********'

# Default email settings
DEFAULT_INSTITUTION_CODE = '223333'
DEFAULT_MFB_CODE = '123422'
DEFAULT_EMAIL_FROM = '<EMAIL>'
```

## Error Handling

All notification functions include comprehensive error handling:

1. **Try-catch blocks** around all notification calls
2. **Logging** of successful and failed notification attempts
3. **Graceful degradation** - system continues to function even if notifications fail
4. **Detailed error messages** in logs for debugging

## Message Templates

### SMS Messages
- **Character limit**: Optimized for SMS length limits
- **Clear and concise**: Essential information only
- **Action-oriented**: Clear next steps when applicable

### Email Messages
- **Professional formatting**: Proper business email structure
- **Detailed information**: Complete transaction/request details
- **Branded**: Consistent Bowen MFB branding
- **Security conscious**: Appropriate warnings and instructions

## Monitoring and Logging

All notification attempts are logged with:
- **Recipient information**: Phone/email (masked for security)
- **Message type**: SMS or email
- **Status**: Success or failure
- **Error details**: For failed attempts
- **Timestamp**: When the notification was sent

## Best Practices

1. **Asynchronous Processing**: All notifications use Celery for background processing
2. **Transaction Safety**: Notifications are sent after database commits
3. **Error Isolation**: Notification failures don't affect core business logic
4. **User Privacy**: Sensitive information is handled appropriately
5. **Rate Limiting**: Built-in protection against notification spam

## Future Enhancements

Potential improvements for the notification system:

1. **User Preferences**: Allow users to choose notification methods
2. **Template Management**: Admin interface for managing message templates
3. **Delivery Tracking**: Track notification delivery status
4. **Retry Mechanisms**: Automatic retry for failed notifications
5. **Push Notifications**: Mobile app push notification support

## Testing

To test the notification system:

1. **Development**: Set DEBUG=True to use tmsaas
2. **Check Logs**: Monitor application logs for notification attempts
3. **Celery Workers**: Ensure Celery workers are running for async processing
4. **Database Changes**: Test by changing status fields in admin interface

## Troubleshooting

Common issues and solutions:

1. **Notifications not sending**: Check Celery worker status
2. **Wrong service used**: Verify DEBUG setting
3. **Missing notifications**: Check signal imports in apps.py
4. **Template errors**: Review message formatting in signal functions
