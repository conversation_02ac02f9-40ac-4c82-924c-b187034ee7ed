from django.contrib import admin
from .models import AdminRole, AdminPermission, AdminUser, AccountCreationRequest


@admin.register(AdminRole)
class AdminRoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    list_filter = ['name', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(AdminPermission)
class AdminPermissionAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'created_at']
    list_filter = ['name', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['id', 'created_at', 'updated_at']


@admin.register(AdminUser)
class AdminUserAdmin(admin.ModelAdmin):
    list_display = ['get_full_name', 'get_email', 'get_role', 'is_active', 'created_at']
    list_filter = ['role', 'is_active', 'created_at']
    search_fields = ['user__first_name', 'user__last_name', 'user__email']
    readonly_fields = ['id', 'created_at', 'updated_at']
    filter_horizontal = ['permissions']

    fieldsets = (
        ("User Information", {
            "fields": ("user", "role", "is_active", "created_by")
        }),
        ("Permissions", {
            "fields": ("permissions",)
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )

    def get_full_name(self, obj):
        return obj.user.get_full_name()

    get_full_name.short_description = "Full Name"

    def get_email(self, obj):
        return obj.user.email

    get_email.short_description = "Email"

    def get_role(self, obj):
        return obj.role.name if obj.role else "No Role"

    get_role.short_description = "Role"


@admin.register(AccountCreationRequest)
class AccountCreationRequestAdmin(admin.ModelAdmin):
    list_display = ['creation_request', 'status', 'get_checked_by', 'get_verified_by', 'get_approved_by', 'created_at']
    list_filter = ['status', 'created_at', 'checked_at', 'verified_at', 'approved_at']
    search_fields = ['creation_request__business_name', 'creation_request__business_email', 'creation_request__business_registration_number']
    readonly_fields = ['id', 'created_at', 'updated_at']

    fieldsets = (
        ("Request Information", {
            "fields": ("creation_request", "status", "remarks")
        }),
        ("Processing Information", {
            "fields": ("checked_by", "checked_at", "verified_by", "verified_at", "approved_by", "approved_at")
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )

    def get_checked_by(self, obj):
        return obj.checked_by.user.get_full_name() if obj.checked_by else "-"

    get_checked_by.short_description = "Checked By"

    def get_verified_by(self, obj):
        return obj.verified_by.user.get_full_name() if obj.verified_by else "-"

    get_verified_by.short_description = "Verified By"

    def get_approved_by(self, obj):
        return obj.approved_by.user.get_full_name() if obj.approved_by else "-"

    get_approved_by.short_description = "Approved By"
