from django.urls import path
from . import views


app_name = "superadmin"

urlpatterns = [
    # Authentication
    path('login', views.AdminLoginAPIView.as_view(), name="admin-login"),

    # Dashboard
    path('dashboard', views.AdminDashboardAPIView.as_view(), name="admin-dashboard"),

    # Company Management
    path('companies', views.CompanyListAPIView.as_view(), name="company-list"),
    path('companies/<uuid:company_id>', views.CompanyListAPIView.as_view(), name="company-detail"),
    path('companies/<uuid:company_id>/customers', views.CompanyCustomersAPIView.as_view(), name="company-customers"),

    # Customer Management
    path('customers', views.CustomerListAPIView.as_view(), name="customer-list"),
    path('customers/set-inactive', views.SetCustomerInactiveAPIView.as_view(), name="set-customer-inactive"),
    path('customers/set-active', views.SetCustomerActiveAPIView.as_view(), name="set-customer-active"),

    # Account Creation Requests
    path('account-requests', views.AccountCreationRequestListAPIView.as_view(), name="account-requests"),
    path('account-requests/process', views.ProcessAccountRequestAPIView.as_view(), name="process-account-request"),

    # Transfer Management
    path('transfers', views.EnhancedTransferListAPIView.as_view(), name="transfer-list"),

    # Bill Payment Management
    path('bill-payments', views.BillPaymentListAPIView.as_view(), name="bill-payment-list"),

    # Admin User Management
    path('admin-users', views.AdminUserListAPIView.as_view(), name="admin-user-list"),
    path('admin-users/create', views.CreateAdminUserAPIView.as_view(), name="create-admin-user"),
    path('admin-users/change-permissions', views.ChangeAdminPermissionAPIView.as_view(), name="change-admin-permissions"),
    path('admin-users/change-password', views.AdminChangePasswordAPIView.as_view(), name="admin-change-password"),
    path('permissions', views.ListAdminPermissionListAPIView.as_view(), name="list-admin-permissions"),

    # Audit Trail
    path('audit-trail', views.AuditTrailListAPIView.as_view(), name="audit-trail"),

    # Update Transaction Limit per account
    path('update-transfer-limit/<uuid:account_id>', views.UpdateTransferLimitAPIView.as_view(), name="update-transfer-limit"),
]

