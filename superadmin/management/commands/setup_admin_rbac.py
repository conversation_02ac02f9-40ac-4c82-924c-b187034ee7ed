from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password

from superadmin.models import AdminRole, AdminPermission, AdminUser
from bowenmfb.modules.choices import ADMIN_ROLE_CHOICES, ADMIN_PERMISSION_CHOICES


class Command(BaseCommand):
    help = 'Set up initial admin roles, permissions, and create a super admin user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-super-admin',
            action='store_true',
            help='Create a super admin user with approver role',
        )
        parser.add_argument(
            '--email',
            type=str,
            help='Email for the super admin user',
        )
        parser.add_argument(
            '--password',
            type=str,
            help='Password for the super admin user',
        )
        parser.add_argument(
            '--first-name',
            type=str,
            help='First name for the super admin user',
        )
        parser.add_argument(
            '--last-name',
            type=str,
            help='Last name for the super admin user',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Admin RBAC system...'))

        # Create admin roles
        self.stdout.write('Creating admin roles...')
        for role_code, role_name in ADMIN_ROLE_CHOICES:
            role, created = AdminRole.objects.get_or_create(
                name=role_code,
                defaults={
                    'description': f'{role_name} role with specific permissions'
                }
            )
            if created:
                self.stdout.write(f'  ✓ Created role: {role_name}')
            else:
                self.stdout.write(f'  - Role already exists: {role_name}')

        # Create admin permissions
        self.stdout.write('Creating admin permissions...')
        for perm_code, perm_name in ADMIN_PERMISSION_CHOICES:
            permission, created = AdminPermission.objects.get_or_create(
                name=perm_code,
                defaults={
                    'description': f'Permission to {perm_name.lower()}'
                }
            )
            if created:
                self.stdout.write(f'  ✓ Created permission: {perm_name}')
            else:
                self.stdout.write(f'  - Permission already exists: {perm_name}')

        # Create super admin user if requested
        if options['create_super_admin']:
            email = options.get('email')
            password = options.get('password')
            first_name = options.get('first_name', 'Super')
            last_name = options.get('last_name', 'Admin')

            if not email or not password:
                self.stdout.write(
                    self.style.ERROR('Email and password are required for creating super admin user')
                )
                return

            # Create Django user
            user, created = User.objects.get_or_create(
                email=email,
                defaults={
                    'username': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'password': make_password(password),
                    'is_staff': True,
                    'is_superuser': True
                }
            )

            if created:
                self.stdout.write(f'  ✓ Created Django user: {email}')
            else:
                self.stdout.write(f'  - Django user already exists: {email}')

            # Create admin user with approver role
            try:
                approver_role = AdminRole.objects.get(name='approver')
                admin_user, created = AdminUser.objects.get_or_create(
                    user=user,
                    defaults={
                        'role': approver_role,
                        'is_active': True
                    }
                )

                if created:
                    # Add all permissions to the super admin
                    all_permissions = AdminPermission.objects.all()
                    admin_user.permissions.set(all_permissions)
                    self.stdout.write(f'  ✓ Created super admin user: {email}')
                    self.stdout.write(f'    Role: Approver')
                    self.stdout.write(f'    Permissions: All ({all_permissions.count()} permissions)')
                else:
                    self.stdout.write(f'  - Admin user already exists: {email}')

            except AdminRole.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR('Approver role not found. Please run the command again.')
                )

        self.stdout.write(self.style.SUCCESS('\nAdmin RBAC setup completed!'))
        
        # Display summary
        self.stdout.write('\nSummary:')
        self.stdout.write(f'  Roles: {AdminRole.objects.count()}')
        self.stdout.write(f'  Permissions: {AdminPermission.objects.count()}')
        self.stdout.write(f'  Admin Users: {AdminUser.objects.count()}')
        
        if AdminUser.objects.exists():
            self.stdout.write('\nExisting Admin Users:')
            for admin_user in AdminUser.objects.all():
                role_name = admin_user.role.get_name_display() if admin_user.role else 'No Role'
                perm_count = admin_user.permissions.count()
                self.stdout.write(f'  - {admin_user.user.get_full_name()} ({admin_user.user.email})')
                self.stdout.write(f'    Role: {role_name}, Permissions: {perm_count}')

        self.stdout.write('\nTo create additional admin users, use the Django admin interface or create them programmatically.')
        self.stdout.write('Default role permissions:')
        self.stdout.write('  Checker: view_companies, check_account_requests, view_transfers')
        self.stdout.write('  Verifier: checker permissions + verify_account_requests')
        self.stdout.write('  Approver: all permissions including manage_admin_permissions')
