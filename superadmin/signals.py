"""
Django signals for superadmin notifications.

This module handles sending notifications for admin-related events.
"""

from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db import transaction

from bowenmfb.modules.utils import send_notification_async, log_request
from .models import AccountCreationRequest, AdminUser


@receiver(post_save, sender=AccountCreationRequest)
def notify_admins_of_new_account_request(sender, instance, created, **kwargs):
    """Notify all admin users when a new account creation request is submitted."""
    if created:
        # Use transaction.on_commit to ensure the notification is sent after the transaction commits
        transaction.on_commit(lambda: send_account_request_notifications(instance))


def send_account_request_notifications(account_request):
    """Send notifications to all admin users about new account creation request."""
    try:
        # Get all active admin users
        admin_users = AdminUser.objects.filter(is_active=True)
        
        if not admin_users.exists():
            log_request("No active admin users found to notify about account creation request")
            return
        
        business_name = account_request.creation_request.business_name
        business_email = account_request.creation_request.business_email
        contact_person = account_request.creation_request.contact_person_name
        
        # Prepare notification messages
        sms_message = f"New account creation request from {business_name}. Contact: {contact_person}. Please review and take action."
        
        email_subject = "New Account Creation Request - Action Required"
        email_body = f"""Dear Admin,

A new account creation request has been submitted and requires your attention.

Company Details:
- Business Name: {business_name}
- Business Email: {business_email}
- Contact Person: {contact_person}
- Contact Phone: {account_request.creation_request.contact_person_phone}
- Business Address: {account_request.creation_request.business_address}
- Registration Number: {account_request.creation_request.business_registration_number}
- Registration Date: {account_request.creation_request.business_registration_date}

Request Status: {account_request.get_status_display()}
Submitted: {account_request.created_at.strftime('%Y-%m-%d %H:%M:%S')}

Please log into the admin portal to review and process this request.

Best regards,
Bowen MFB System"""
        
        # Send notifications to all admin users
        for admin_user in admin_users:
            try:
                # Send SMS notification
                send_notification_async(
                    notification_type='sms',
                    recipient=admin_user.user.username,  # Assuming username is phone number
                    message=sms_message
                )
                
                # Send email notification
                send_notification_async(
                    notification_type='email',
                    recipient=admin_user.user.email,
                    message=email_body,
                    subject=email_subject
                )
                
            except Exception as e:
                log_request(f"Failed to send notification to admin {admin_user.user.email}: {str(e)}")
        
        log_request(f"Sent account creation request notifications to {admin_users.count()} admin users for request {account_request.id}")
        
    except Exception as e:
        log_request(f"Failed to send account creation request notifications: {str(e)}")


@receiver(post_save, sender=AdminUser)
def notify_admin_user_created(sender, instance, created, **kwargs):
    """Send welcome notification to newly created admin users."""
    if created:
        transaction.on_commit(lambda: send_admin_welcome_notification(instance))


def send_admin_welcome_notification(admin_user):
    """Send welcome notification to newly created admin user."""
    try:
        user = admin_user.user
        role_name = admin_user.role.get_name_display() if admin_user.role else "No Role Assigned"
        permissions = list(admin_user.permissions.values_list('name', flat=True))
        
        # Prepare welcome messages
        sms_message = f"Welcome to Bowen MFB Admin Portal! Your admin account has been created with role: {role_name}. Please check your email for details."
        
        email_subject = "Welcome to Bowen MFB Admin Portal"
        email_body = f"""Dear {user.get_full_name()},

Welcome to the Bowen MFB Admin Portal!

Your admin account has been successfully created with the following details:

Account Information:
- Name: {user.get_full_name()}
- Email: {user.email}
- Role: {role_name}
- Status: Active

Assigned Permissions:
{chr(10).join([f"- {perm.replace('_', ' ').title()}" for perm in permissions]) if permissions else "- No specific permissions assigned"}

You can now log into the admin portal using your email address and the password provided to you separately.

Please contact your system administrator if you have any questions or need assistance.

Best regards,
Bowen MFB Team"""
        
        # Send SMS notification
        send_notification_async(
            notification_type='sms',
            recipient=user.username,  # Assuming username is phone number
            message=sms_message
        )
        
        # Send email notification
        send_notification_async(
            notification_type='email',
            recipient=user.email,
            message=email_body,
            subject=email_subject
        )
        
        log_request(f"Sent welcome notification to new admin user {user.email}")
        
    except Exception as e:
        log_request(f"Failed to send admin welcome notification: {str(e)}")
