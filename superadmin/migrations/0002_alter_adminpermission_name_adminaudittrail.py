# Generated by Django 5.2.1 on 2025-07-06 21:13

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('superadmin', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='adminpermission',
            name='name',
            field=models.CharField(choices=[('view_companies', 'View Companies'), ('manage_company_users', 'Set Company Users as Inactive/Active'), ('check_account_requests', 'Check Account Creation Requests'), ('verify_account_requests', 'Verify Account Creation Requests'), ('approve_account_requests', 'Approve Account Creation Requests'), ('view_transfers', 'List Company Transfers'), ('view_bill_payments', 'List Company Bill Payments'), ('manage_admin_permissions', 'Change Admin Permissions'), ('create_admin_users', 'Create New Admin Users'), ('view_audit_trail', 'View Admin Audit Trail')], max_length=100, unique=True),
        ),
        migrations.CreateModel(
            name='AdminAuditTrail',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('action', models.CharField(choices=[('login', 'Admin Login'), ('logout', 'Admin Logout'), ('create_admin', 'Create Admin User'), ('update_admin', 'Update Admin User'), ('change_permissions', 'Change Admin Permissions'), ('set_customer_active', 'Set Customer Active'), ('set_customer_inactive', 'Set Customer Inactive'), ('check_account_request', 'Check Account Request'), ('verify_account_request', 'Verify Account Request'), ('approve_account_request', 'Approve Account Request'), ('decline_account_request', 'Decline Account Request'), ('view_transfers', 'View Transfers'), ('view_bill_payments', 'View Bill Payments'), ('change_password', 'Change Password'), ('view_customers', 'View Customers'), ('view_companies', 'View Companies'), ('view_audit_trail', 'View Audit Trail')], max_length=50)),
                ('description', models.TextField()),
                ('target_model', models.CharField(blank=True, max_length=100, null=True)),
                ('target_id', models.CharField(blank=True, max_length=100, null=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('additional_data', models.JSONField(blank=True, null=True)),
                ('admin_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='superadmin.adminuser')),
            ],
            options={
                'verbose_name': 'Admin Audit Trail',
                'verbose_name_plural': 'Admin Audit Trails',
                'ordering': ['-created_at'],
            },
        ),
    ]
