# Generated by Django 5.2.1 on 2025-07-12 22:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('superadmin', '0002_alter_adminpermission_name_adminaudittrail'),
    ]

    operations = [
        migrations.AlterField(
            model_name='adminpermission',
            name='name',
            field=models.CharField(choices=[('view_companies', 'View Companies'), ('manage_company_users', 'Set Company Users as Inactive/Active'), ('check_account_requests', 'Check Account Creation Requests'), ('verify_account_requests', 'Verify Account Creation Requests'), ('approve_account_requests', 'Approve Account Creation Requests'), ('view_transfers', 'List Company Transfers'), ('view_bill_payments', 'List Company Bill Payments'), ('manage_admin_permissions', 'Change Admin Permissions'), ('create_admin_users', 'Create New Admin Users'), ('view_audit_trail', 'View Admin Audit Trail'), ('update_transfer_limit', 'Update Transfer Limit')], max_length=100, unique=True),
        ),
    ]
