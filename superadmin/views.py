from django.db.models import Q, Sum, Count
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from account.models import Company, Customer
from billpayment.models import BillPaymentRequest, BillPayment
from bowenmfb.modules.paginations import CustomPagination
from transfer.models import SingleTransferRequest, BulkTransferRequest
from bowenmfb.modules.exceptions import raise_serializer_error_msg
from bowenmfb.modules.permissions import (
    IsAdminUser, CanViewCompanies, CanManageCompanyUsers,
    CanCheckAccountRequests, CanVerifyAccountRequests, CanApproveAccountRequests,
    CanViewTransfers, CanManageAdminPermissions, CanCreateAdminUsers,
    CanViewBillPayments, CanViewAuditTrail, CanUpdateTransferLimit
)
from .models import AdminUser, AdminAuditTrail
from .serializers import *


def log_admin_action(admin_user, action, description, target_model=None, target_id=None,
                    request=None, additional_data=None):
    """Utility function to log admin actions"""
    ip_address = None
    user_agent = None

    if request:
        # Get IP address
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')

        # Get user agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')

    AdminAuditTrail.objects.create(
        admin_user=admin_user,
        action=action,
        description=description,
        target_model=target_model,
        target_id=target_id,
        ip_address=ip_address,
        user_agent=user_agent,
        additional_data=additional_data
    )


class AdminLoginAPIView(APIView):
    permission_classes = []

    @extend_schema(request=AdminLoginSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = AdminLoginSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin login
        try:
            from django.contrib.auth.models import User
            user = User.objects.get(email=request.data.get('email'))
            admin_user = AdminUser.objects.get(user=user, is_active=True)
            log_admin_action(
                admin_user=admin_user,
                action='login',
                description=f"Admin user logged in: {user.get_full_name()}",
                request=request
            )
        except (User.DoesNotExist, AdminUser.DoesNotExist):
            pass

        return Response(response)


class CompanyListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(responses={status.HTTP_200_OK: CompanySerializerOut(many=True)})
    def get(self, request, company_id=None):
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
                serializer = CompanySerializerOut(company)
                return Response({
                    "detail": "Company retrieved successfully",
                    "data": serializer.data
                })
            except Company.DoesNotExist:
                return Response({"detail": "Company not found"}, status=status.HTTP_404_NOT_FOUND)


        queryset = Company.objects.all().order_by('-created_at')
        companies = self.paginate_queryset(queryset, request)
        serializer = CompanySerializerOut(companies, many=True)
        return Response({
            "detail": "Companies retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class CompanyCustomersAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(responses={status.HTTP_200_OK: CustomerSerializerOut(many=True)})
    def get(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response({"detail": "Company not found"}, status=status.HTTP_404_NOT_FOUND)

        customers = Customer.objects.filter(company=company).order_by('-created_at')
        serializer = CustomerSerializerOut(customers, many=True)
        return Response({
            "detail": "Company customers retrieved successfully",
            "company": company.name,
            "data": serializer.data
        })


class SetCustomerInactiveAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageCompanyUsers]

    @extend_schema(request=SetCustomerInactiveSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = SetCustomerInactiveSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='set_customer_inactive',
            description=f"Set customer as inactive: {request.data.get('customer_id')}",
            target_model='Customer',
            target_id=str(request.data.get('customer_id')),
            request=request
        )

        return Response(response)


class AccountCreationRequestListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated, IsAdminUser, CanCheckAccountRequests]

    @extend_schema(
        parameters=[OpenApiParameter(name="status", type=str, required=False)],
        responses={status.HTTP_200_OK: AccountCreationRequestSerializerOut(many=True)}
    )

    def get(self, request):
        # Filter by status if provided
        status_filter = request.query_params.get('status', None)
        requests = AccountCreationRequest.objects.all()

        if status_filter:
            requests = requests.filter(status=status_filter)

        requests = requests.order_by('-created_at')
        queryset = self.paginate_queryset(requests, request)
        serializer = AccountCreationRequestSerializerOut(queryset, many=True)
        return Response({
            "detail": "Account creation requests retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class ProcessAccountRequestAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get_permissions(self):
        permissions = [IsAuthenticated, IsAdminUser]

        if hasattr(self, 'request') and self.request.data:
            action = self.request.data.get('action')
            if action == 'check':
                permissions.append(CanCheckAccountRequests)
            elif action == 'verify':
                permissions.append(CanVerifyAccountRequests)
            elif action in ['approve', 'decline']:
                permissions.append(CanApproveAccountRequests)

        return [permission() for permission in permissions]

    @extend_schema(request=ProcessAccountRequestSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ProcessAccountRequestSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class TransferListAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewTransfers]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        transfer_type = request.query_params.get('type', 'all')  # 'single', 'bulk', or 'all'

        response_data = {}

        # Filter single transfers
        single_transfers = SingleTransferRequest.objects.all()
        if company_id:
            single_transfers = single_transfers.filter(company_id=company_id)

        # Filter bulk transfers
        bulk_transfers = BulkTransferRequest.objects.all()
        if company_id:
            bulk_transfers = bulk_transfers.filter(company_id=company_id)

        if transfer_type == 'single':
            single_serializer = SingleTransferSerializerOut(single_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "Single transfers retrieved successfully",
                "single_transfers": single_serializer.data
            }
        elif transfer_type == 'bulk':
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "Bulk transfers retrieved successfully",
                "bulk_transfers": bulk_serializer.data
            }
        else:  # 'all'
            single_serializer = SingleTransferSerializerOut(single_transfers.order_by('-created_at'), many=True)
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers.order_by('-created_at'), many=True)
            response_data = {
                "detail": "All transfers retrieved successfully",
                "single_transfers": single_serializer.data,
                "bulk_transfers": bulk_serializer.data
            }

        return Response(response_data)


class AdminUserListAPIView(APIView, CustomPagination):
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(responses={status.HTTP_200_OK: AdminUserSerializerOut(many=True)})
    def get(self, request):
        admin_users = AdminUser.objects.filter(is_active=True).order_by('-created_at')
        queryset = self.paginate_queryset(admin_users, request)
        serializer = AdminUserSerializerOut(queryset, many=True)
        return Response({
            "detail": "Admin users retrieved successfully",
            "data": self.get_paginated_response(serializer.data).data
        })


class ChangeAdminPermissionAPIView(APIView):
    """Change admin user permissions - requires manage_admin_permissions permission"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageAdminPermissions]

    @extend_schema(request=ChangeAdminPermissionSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get current admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = ChangeAdminPermissionSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class AdminDashboardAPIView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    @extend_schema(responses={status.HTTP_200_OK})
    def get(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Gather statistics based on permissions
        dashboard_data = {
            "admin_info": {
                "name": admin_user.user.get_full_name(),
                "role": admin_user.role.name if admin_user.role else None,
                "permissions": list(admin_user.permissions.values_list('name', flat=True))
            },
            "total_companies": Company.objects.count(),
            "customers": {
                "total": Customer.objects.count(),
                "active": Customer.objects.filter(active=True).count(),
                "verified": Customer.objects.filter(is_verified=True).count()
            },
            "account_requests": {
                "pending": AccountCreationRequest.objects.filter(status='pending').count(),
                "approved": AccountCreationRequest.objects.filter(status='success').count(),
                "declined": AccountCreationRequest.objects.filter(status='failed').count()
            },
            "transfers": {
                "single_pending": SingleTransferRequest.objects.filter(is_approved=False, is_declined=False).count(),
                "single_approved": SingleTransferRequest.objects.filter(is_approved=True).count(),
                "bulk_pending": BulkTransferRequest.objects.filter(is_approved=False, is_declined=False).count(),
                "bulk_approved": BulkTransferRequest.objects.filter(is_approved=True).count()
            }
        }

        # Add statistics

        return Response({
            "detail": "Dashboard data retrieved successfully",
            "data": dashboard_data
        })


class ListAdminPermissionListAPIView(generics.ListAPIView):
    permission_classes = [IsAuthenticated, CanManageAdminPermissions]
    queryset = AdminPermission.objects.all().order_by("-name")
    serializer_class = AdminPermissionSerializerOut


class CreateAdminUserAPIView(APIView):
    """Create new admin user - requires create_admin_users permission"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanCreateAdminUsers]

    @extend_schema(request=CreateAdminUserSerializerIn, responses={status.HTTP_201_CREATED})
    def post(self, request):
        # Get current admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = CreateAdminUserSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='create_admin',
            description=f"Created new admin user: {response['data']['full_name']}",
            target_model='AdminUser',
            target_id=response['data']['id'],
            request=request
        )

        return Response(response, status=status.HTTP_201_CREATED)


class CustomerListAPIView(APIView, CustomPagination):
    """List all customers with search functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewCompanies]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="search", type=str), OpenApiParameter(name="company_id", type=str), OpenApiParameter(name="active", type=str),
            OpenApiParameter(name="verified", type=str)
        ],
        responses={status.HTTP_200_OK: CustomerSerializerOut(many=True)}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        search = request.query_params.get('search', '')
        company_id = request.query_params.get('company_id', '')
        active_filter = request.query_params.get('active', '')
        verified_filter = request.query_params.get('verified', '')

        # Base queryset
        customers = Customer.objects.all()

        # Apply search filter
        if search:
            customers = customers.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(user__email__icontains=search) |
                Q(phone_number__icontains=search) |
                Q(company__name__icontains=search)
            )

        # Apply company filter
        if company_id:
            customers = customers.filter(company_id=company_id)

        # Apply active filter
        if active_filter.lower() == 'true':
            customers = customers.filter(active=True)
        elif active_filter.lower() == 'false':
            customers = customers.filter(active=False)

        # Apply verified filter
        if verified_filter.lower() == 'true':
            customers = customers.filter(is_verified=True)
        elif verified_filter.lower() == 'false':
            customers = customers.filter(is_verified=False)

        customers = customers.order_by('-created_at')
        queryset = self.paginate_queryset(customers, request)
        serializer = CustomerSerializerOut(queryset, many=True)

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_customers',
            description=f"Viewed customers list with filters: search='{search}', company_id='{company_id}'",
            request=request,
            additional_data={
                'search': search,
                'company_id': company_id,
                'active_filter': active_filter,
                'verified_filter': verified_filter,
                'total_results': customers.count()
            }
        )

        return Response({
            "detail": "Customers retrieved successfully",
            "total": customers.count(),
            "data": self.get_paginated_response(serializer.data).data
        })


class SetCustomerActiveAPIView(APIView):
    """Set customer as active - complement to existing inactive functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanManageCompanyUsers]

    @extend_schema(request=SetCustomerActiveSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = SetCustomerActiveSerializerIn(data=request.data)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='set_customer_active',
            description=f"Set customer as active: {request.data.get('customer_id')}",
            target_model='Customer',
            target_id=str(request.data.get('customer_id')),
            request=request
        )

        return Response(response)


class EnhancedTransferListAPIView(APIView, CustomPagination):
    """Enhanced transfer list with search functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewTransfers]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="company_id", type=str, required=False),
            OpenApiParameter(name="type", type=str, required=True,
                             description="Select between single or bulk transfers or all. Choices are 'single', 'bulk' and 'all'"),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="status", type=str, required=False)
        ],
        responses={status.HTTP_200_OK}
    )

    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        transfer_type = request.query_params.get('type', 'all')  # 'single', 'bulk', or 'all'
        search = request.query_params.get('search', '')
        status_filter = request.query_params.get('status', '')  # 'pending', 'approved', 'declined'

        response_data = {}

        # Filter single transfers
        if transfer_type == 'single':

            single_transfers = SingleTransferRequest.objects.all()
            if company_id:
                single_transfers = single_transfers.filter(company_id=company_id)
            if search:
                single_transfers = single_transfers.filter(
                    Q(beneficiary_name__icontains=search) |
                    Q(beneficiary_account_number__icontains=search) |
                    Q(description__icontains=search) |
                    Q(company__name__icontains=search)
                )
            if status_filter == 'pending':
                single_transfers = single_transfers.filter(is_approved=False, is_declined=False)
            if status_filter == 'approved':
                single_transfers = single_transfers.filter(is_approved=True)
            if status_filter == 'declined':
                single_transfers = single_transfers.filter(is_declined=True)

            queryset = self.paginate_queryset(single_transfers.order_by('-created_at'), request)
            single_serializer = SingleTransferSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Single transfers retrieved successfully",
                "total": single_transfers.count(),
                "single_transfers": self.get_paginated_response(single_serializer.data).data
            }

        elif transfer_type == 'bulk':

            # Filter bulk transfers
            bulk_transfers = BulkTransferRequest.objects.all()
            if company_id:
                bulk_transfers = bulk_transfers.filter(company_id=company_id)
            if search:
                bulk_transfers = bulk_transfers.filter(
                    Q(description__icontains=search) |
                    Q(company__name__icontains=search)
                )
            if status_filter == 'pending':
                bulk_transfers = bulk_transfers.filter(is_approved=False, is_declined=False)
            elif status_filter == 'approved':
                bulk_transfers = bulk_transfers.filter(is_approved=True)
            elif status_filter == 'declined':
                bulk_transfers = bulk_transfers.filter(is_declined=True)

            queryset = self.paginate_queryset(bulk_transfers.order_by('-created_at'), request)
            bulk_serializer = BulkTransferSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Bulk transfers retrieved successfully",
                "total": bulk_transfers.count(),
                "bulk_transfers": self.get_paginated_response(bulk_serializer.data).data
            }

        else:  # 'all'
            single_transfers = SingleTransferRequest.objects.all().order_by('-created_at')[:50]
            bulk_transfers = BulkTransferRequest.objects.all().order_by('-created_at')[:50]
            single_serializer = SingleTransferSerializerOut(single_transfers, many=True)
            bulk_serializer = BulkTransferSerializerOut(bulk_transfers, many=True)
            response_data = {
                "detail": "All transfers retrieved successfully",
                "single_total": SingleTransferRequest.objects.count(),
                "bulk_total": BulkTransferRequest.objects.count(),
                "single_transfers": single_serializer.data,
                "bulk_transfers": bulk_serializer.data
            }

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_transfers',
            description=f"Viewed transfers with filters: type='{transfer_type}', company_id='{company_id}', search='{search}'",
            request=request,
            additional_data={
                'transfer_type': transfer_type,
                'company_id': company_id,
                'search': search,
                'status_filter': status_filter
            }
        )

        return Response(response_data)


class BillPaymentListAPIView(APIView, CustomPagination):
    """List all bill payments with search and filtering functionality"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewBillPayments]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="company_id", type=str, required=False),
            OpenApiParameter(name="type", type=str, required=True,
                             description="Select between request or payments transfers or all. Choices are 'requests', 'payments' and 'all'"),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="status", type=str, required=False)
        ],
        responses={status.HTTP_200_OK}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        company_id = request.query_params.get('company_id', None)
        search = request.query_params.get('search', '')
        status_filter = request.query_params.get('status', '')
        payment_type = request.query_params.get('type', 'all')  # 'requests', 'payments', or 'all'

        response_data = {}
        total_amount = BillPayment.objects.all().aggregate(total=Sum('amount'))['total'] or 0
        total_count = BillPayment.objects.count()
        # Filter bill payment requests
        if payment_type == 'requests':
            bill_requests = BillPaymentRequest.objects.all()
            if company_id:
                bill_requests = bill_requests.filter(company_id=company_id)
            if search:
                bill_requests = bill_requests.filter(
                    Q(customer_name__icontains=search) |
                    Q(customer_id__icontains=search) |
                    Q(biller__name__icontains=search) |
                    Q(company__name__icontains=search) |
                    Q(description__icontains=search)
                )
            if status_filter == 'pending':
                bill_requests = bill_requests.filter(is_approved=False, is_declined=False)
            elif status_filter == 'approved':
                bill_requests = bill_requests.filter(is_approved=True)
            elif status_filter == 'declined':
                bill_requests = bill_requests.filter(is_declined=True)
            queryset = self.paginate_queryset(bill_requests.order_by('-created_at'), request)
            requests_serializer = BillPaymentRequestSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Bill payment requests retrieved successfully",
                "total": bill_requests.count(),
                "bill_payment_requests": self.get_paginated_response(requests_serializer.data).data
            }

        elif payment_type == 'payments':
            # Filter executed bill payments
            bill_payments = BillPayment.objects.all()
            if company_id:
                bill_payments = bill_payments.filter(company_id=company_id)
            if search:
                bill_payments = bill_payments.filter(
                    Q(customer_name__icontains=search) |
                    Q(customer_id__icontains=search) |
                    Q(biller_name__icontains=search) |
                    Q(company__name__icontains=search)
                )
            if status_filter:
                bill_payments = bill_payments.filter(status=status_filter)

            # Calculate totals
            total_amount = bill_payments.aggregate(total=Sum('amount'))['total'] or 0
            total_count = bill_payments.count()

            queryset = self.paginate_queryset(bill_payments.order_by('-created_on'), request)
            payments_serializer = BillPaymentSerializerOut(queryset, many=True)
            response_data = {
                "detail": "Bill payments retrieved successfully",
                "total_count": total_count,
                "total_amount": total_amount,
                "bill_payments": self.get_paginated_response(payments_serializer.data).data
            }

        else:  # 'all'
            bill_requests = BillPaymentRequest.objects.all().order_by('-created_at')[:50]
            bill_payments = BillPayment.objects.all().order_by('-created_on')[:50]
            requests_serializer = BillPaymentRequestSerializerOut(bill_requests, many=True)
            payments_serializer = BillPaymentSerializerOut(bill_payments, many=True)
            response_data = {
                "detail": "All bill payment data retrieved successfully",
                "requests_total": BillPaymentRequest.objects.count(),
                "payments_total_count": BillPayment.objects.count(),
                "payments_total_amount": BillPayment.objects.all().aggregate(total=Sum('amount'))['total'] or 0,
                "bill_payment_requests": requests_serializer.data,
                "bill_payments": payments_serializer.data
            }

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='view_bill_payments',
            description=f"Viewed bill payments with filters: type='{payment_type}', company_id='{company_id}', search='{search}'",
            request=request,
            additional_data={
                'payment_type': payment_type,
                'company_id': company_id,
                'search': search,
                'status_filter': status_filter,
                'total_amount': total_amount,
                'total_count': total_count
            }
        )

        return Response(response_data)


class AuditTrailListAPIView(APIView, CustomPagination):
    """List admin audit trail with filtering"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanViewAuditTrail]

    @extend_schema(
        parameters=[
            OpenApiParameter(name="admin_id", type=str, required=False),
            OpenApiParameter(name="action", type=str, required=True),
            OpenApiParameter(name="search", type=str, required=False),
            OpenApiParameter(name="start_date", type=str, required=False),
            OpenApiParameter(name="end_date", type=str, required=False)
        ],
        responses={status.HTTP_200_OK: AdminAuditTrailSerializerOut(many=True)}
    )
    def get(self, request):
        # Get admin user for logging
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        admin_id = request.query_params.get('admin_id', '')
        action = request.query_params.get('action', '')
        search = request.query_params.get('search', '')
        start_date = request.query_params.get('start_date', '')
        end_date = request.query_params.get('end_date', '')

        # Base queryset
        audit_trails = AdminAuditTrail.objects.all()

        # Apply filters
        if admin_id:
            audit_trails = audit_trails.filter(admin_user_id=admin_id)

        if action:
            audit_trails = audit_trails.filter(action=action)

        if search:
            audit_trails = audit_trails.filter(
                Q(description__icontains=search) |
                Q(admin_user__user__first_name__icontains=search) |
                Q(admin_user__user__last_name__icontains=search) |
                Q(admin_user__user__email__icontains=search)
            )

        if start_date:
            try:
                from datetime import datetime
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
                audit_trails = audit_trails.filter(created_at__gte=start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                from datetime import datetime
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
                audit_trails = audit_trails.filter(created_at__lte=end_date_obj)
            except ValueError:
                pass

        audit_trails = audit_trails.order_by('-created_at')
        queryset = self.paginate_queryset(audit_trails, request)
        serializer = AdminAuditTrailSerializerOut(queryset, many=True)

        # Log admin action (but don't create infinite loop)
        if action != 'view_audit_trail':  # Prevent logging when viewing audit trail
            log_admin_action(
                admin_user=admin_user,
                action='view_audit_trail',
                description=f"Viewed audit trail with filters: admin_id='{admin_id}', action='{action}', search='{search}'",
                request=request,
                additional_data={
                    'admin_id': admin_id,
                    'action': action,
                    'search': search,
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_results': audit_trails.count()
                }
            )

        return Response({
            "detail": "Audit trail retrieved successfully",
            "total": audit_trails.count(),
            "data": self.get_paginated_response(serializer.data).data
        })


class AdminChangePasswordAPIView(APIView):
    """Change admin user password"""
    permission_classes = [IsAuthenticated, IsAdminUser]

    @extend_schema(request=AdminChangePasswordSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        serializer = AdminChangePasswordSerializerIn(
            data=request.data,
            context={'admin_user': admin_user}
        )
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        # Log admin action
        log_admin_action(
            admin_user=admin_user,
            action='change_password',
            description="Changed login password",
            target_model='User',
            target_id=str(admin_user.user.id),
            request=request
        )

        return Response(response)


class UpdateTransferLimitAPIView(APIView):
    """Update account transfer limit"""
    permission_classes = [IsAuthenticated, IsAdminUser, CanUpdateTransferLimit]

    @extend_schema(request=UpdateAccountTransferLimitSerializerIn, responses={status.HTTP_200_OK})
    def put(self, request, account_id):
        # Get admin user
        try:
            admin_user = AdminUser.objects.get(user=request.user, is_active=True)
        except AdminUser.DoesNotExist:
            return Response({"detail": "Admin user not found"}, status=status.HTTP_403_FORBIDDEN)

        try:
            account = CompanyAccount.objects.get(id=account_id)
        except CompanyAccount.DoesNotExist:
            raise InvalidRequestException({"detail": "Account not found"})

        serializer = UpdateAccountTransferLimitSerializerIn(data=request.data, instance=account)
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()

        log_admin_action(
            admin_user=admin_user,
            action='update_transfer_limit',
            description=f"Updated transfer limit for account {account_id}",
            target_model='CompanyAccount',
            target_id=str(account_id),
            request=request
        )

        return Response(response)
