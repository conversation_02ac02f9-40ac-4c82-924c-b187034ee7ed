from django.contrib import admin
from .models import *


@admin.register(BankConstantTable)
class BankConstantTableModelAdmin(admin.ModelAdmin):
    list_display = ["name", "short_name", "support_email", "app_version", "created_at", "updated_at"]
    list_filter = ["app_version", "created_at", "updated_at"]
    search_fields = ["name", "short_name", "support_email", "website"]
    readonly_fields = ["id", "created_at", "updated_at"]

    fieldsets = (
        ("Basic Information", {
            "fields": ("name", "short_name", "logo")
        }),
        ("Contact Information", {
            "fields": ("support_email", "support_phone", "website", "address")
        }),
        ("Banking Configuration", {
            "fields": ("savings_product_code", "transfer_fee", "institution_code", "mfb_code", "corporate_product_code", "app_version")
        }),
        ("API Configuration", {
            "fields": ("auth_token", "dev_base_url", "prod_base_url"),
            "description": "BankOne API configuration settings"
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )


@admin.register(AccountOfficer)
class AccountOfficerModelAdmin(admin.ModelAdmin):
    list_display = ["code", "name", "email", "gender", "phone_number", "created_at"]
    list_filter = ["gender", "created_at", "updated_at"]
    search_fields = ["code", "name", "email", "phone_number"]
    readonly_fields = ["id", "created_at", "updated_at"]

    fieldsets = (
        ("Officer Information", {
            "fields": ("code", "name", "email", "gender", "phone_number")
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )


@admin.register(Company)
class CompanyModelAdmin(admin.ModelAdmin):
    list_display = ["name", "bank_customer_id", "email", "phone_number", "active", "created_at"]
    list_filter = ["active", "created_at", "updated_at", "registration_date"]
    search_fields = ["name", "bank_customer_id", "email", "phone_number", "website"]
    readonly_fields = ["id", "created_at", "updated_at", "get_masked_tax_number", "get_registration_number"]

    fieldsets = (
        ("Company Information", {
            "fields": ("name", "bank_customer_id", "active")
        }),
        ("Contact Information", {
            "fields": ("email", "phone_number", "website", "address")
        }),
        ("Registration Details", {
            "fields": ("tax_number", "get_masked_tax_number", "registration_number", "get_registration_number", "registration_date"),
            "description": "Tax and registration information (sensitive data is masked in readonly fields)"
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )

    def get_masked_tax_number(self, obj):
        return obj.get_masked_tax_number
    get_masked_tax_number.short_description = "Masked Tax Number"

    def get_registration_number(self, obj):
        return obj.get_registration_number
    get_registration_number.short_description = "Masked Registration Number"


@admin.register(Customer)
class CustomerModelAdmin(admin.ModelAdmin):
    list_display = ["get_full_name", "company__name", "user_email", "phone_number", "is_verified", "active", "created_at"]
    list_filter = ["is_verified", "active", "gender", "created_at", "updated_at", "company"]
    search_fields = ["user__first_name", "user__last_name", "user__email", "phone_number", "other_name"]
    readonly_fields = ["id", "created_at", "updated_at", "get_masked_bvn", "get_masked_nin", "verification_token", "approval_pin"]

    fieldsets = (
        ("User Information", {
            "fields": ("user", "other_name", "company")
        }),
        ("Personal Details", {
            "fields": ("address", "dob", "gender", "phone_number", "state_of_origin", "image")
        }),
        ("Identity & Verification", {
            "fields": ("bvn_number", "get_masked_bvn", "nin_number", "get_masked_nin", "is_verified", "active"),
            "description": "Sensitive identity information (BVN/NIN are encrypted, masked versions shown in readonly fields)"
        }),
        ("Security", {
            "fields": ("verification_token", "verification_token_expiry", "approval_pin"),
            "classes": ("collapse",),
            "description": "Security tokens and pins (encrypted)"
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = "Email"

    def get_masked_bvn(self, obj):
        return obj.get_masked_bvn
    get_masked_bvn.short_description = "Masked BVN"

    def get_masked_nin(self, obj):
        return obj.get_masked_nin
    get_masked_nin.short_description = "Masked NIN"


@admin.register(CompanyCreationRequest)
class CompanyCreationRequestModelAdmin(admin.ModelAdmin):
    list_display = ["business_name", "business_email", "business_phone_number", "created_at"]
    list_filter = ["created_at", "updated_at", "business_registration_date"]
    search_fields = ["business_name", "business_email", "business_phone_number", "contact_person_name", "business_sector"]
    readonly_fields = ["id", "created_at", "updated_at", "account_creation_response"]

    fieldsets = (
        ("Business Information", {
            "fields": ("business_name", "business_email", "business_phone_number", "business_address", "business_website", "business_sector")
        }),
        ("Contact Person", {
            "fields": ("contact_person_name", "contact_person_phone")
        }),
        ("Registration Details", {
            "fields": ("business_tax_number", "business_registration_number", "business_registration_date")
        }),
        ("Corporate Structure", {
            "fields": ("signatories", "directors"),
            "classes": ("collapse",)
        }),
        ("System Response", {
            "fields": ("account_creation_response",),
            "classes": ("collapse",)
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )


@admin.register(CompanyAccount)
class CompanyAccountModelAdmin(admin.ModelAdmin):
    list_display = ["company_name", "account_number", "bank_one_account_number", "active", "account_officer", "created_at"]
    list_filter = ["active", "created_at", "updated_at"]
    search_fields = ["company__name", "account_number", "bank_one_account_number"]
    readonly_fields = ["id", "created_at", "updated_at", "get_masked_account_number"]

    fieldsets = (
        ("Account Information", {
            "fields": ("company", "account_number", "get_masked_account_number", "bank_one_account_number", "active")
        }),
        ("Account Management", {
            "fields": ("account_officer",)
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )

    def company_name(self, obj):
        return obj.company.name if obj.company else "No Company"
    company_name.short_description = "Company"

    def get_masked_account_number(self, obj):
        return obj.get_masked_account_number
    get_masked_account_number.short_description = "Masked Account Number"


@admin.register(InAppNotification)
class InAppNotificationAdmin(admin.ModelAdmin):
    list_display = ['company', 'title', 'notification_type', 'is_read', 'created_at']
    list_filter = ['notification_type', 'is_read', 'created_at', 'company']
    search_fields = ['title', 'message', 'company__name']
    readonly_fields = ['id', 'created_at', 'updated_at', 'read_at']

    fieldsets = (
        ("Notification Information", {
            "fields": ("company", "title", "message", "notification_type")
        }),
        ("Status", {
            "fields": ("is_read", "read_at")
        }),
        ("Related Object", {
            "fields": ("related_object_id", "related_object_type"),
            "classes": ("collapse",)
        }),
        ("System Information", {
            "fields": ("id", "created_at", "updated_at"),
            "classes": ("collapse",)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('company')


