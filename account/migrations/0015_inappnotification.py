# Generated by Django 5.2.1 on 2025-07-13 12:24

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0014_companyaccount_daily_transfer_limit_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='InAppNotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('transfer_created', 'Transfer Created'), ('transfer_checked', 'Transfer Checked'), ('transfer_verified', 'Transfer Verified'), ('transfer_approved', 'Transfer Approved'), ('bill_payment_created', 'Bill Payment Created'), ('bill_payment_checked', 'Bill Payment Checked'), ('bill_payment_verified', 'Bill Payment Verified'), ('bill_payment_approved', 'Bill Payment Approved'), ('user_signup', 'User Signup'), ('transfer_limit_updated', 'Transfer Limit Updated'), ('general', 'General')], default='general', max_length=50)),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('related_object_id', models.UUIDField(blank=True, help_text='ID of related object (transfer, bill payment, etc.)', null=True)),
                ('related_object_type', models.CharField(blank=True, help_text='Type of related object', max_length=50, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='account.company')),
            ],
            options={
                'verbose_name': 'In-App Notification',
                'verbose_name_plural': 'In-App Notifications',
                'ordering': ['-created_at'],
            },
        ),
    ]
