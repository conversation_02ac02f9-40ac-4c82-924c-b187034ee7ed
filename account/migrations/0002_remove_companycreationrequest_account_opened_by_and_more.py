# Generated by Django 5.2.1 on 2025-05-31 08:50

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='companycreationrequest',
            name='account_opened_by',
        ),
        migrations.AddField(
            model_name='companycreationrequest',
            name='account_creation_response',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companycreationrequest',
            name='contact_person_name',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='companycreationrequest',
            name='contact_person_phone',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='companycreationrequest',
            name='directors',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='companycreationrequest',
            name='signatories',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='company',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='account.company'),
        ),
        migrations.AddField(
            model_name='customer',
            name='is_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='customer',
            name='verification_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='verification_token_expiry',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='bank_customer_id',
            field=models.CharField(max_length=200, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='customer',
            name='approval_pin',
            field=models.TextField(blank=True, null=True),
        ),
    ]
