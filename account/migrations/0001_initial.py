# Generated by Django 5.2.1 on 2025-05-29 00:02

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountOfficer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=100)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('email', models.CharField(blank=True, max_length=300, null=True)),
                ('gender', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BankConstantTable',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=200)),
                ('short_name', models.CharField(blank=True, max_length=50, null=True)),
                ('support_email', models.CharField(blank=True, max_length=50, null=True)),
                ('support_phone', models.CharField(blank=True, max_length=50, null=True)),
                ('website', models.CharField(blank=True, max_length=50, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('logo', models.ImageField(blank=True, null=True, upload_to='bank-logo')),
                ('savings_product_code', models.IntegerField(blank=True, null=True)),
                ('transfer_fee', models.FloatField(default=0.0)),
                ('auth_token', models.TextField(blank=True, null=True)),
                ('institution_code', models.TextField(blank=True, null=True)),
                ('mfb_code', models.TextField(blank=True, null=True)),
                ('app_version', models.IntegerField(default=1)),
                ('dev_base_url', models.URLField(blank=True, help_text='Development/Staging BankOne API base URL', null=True)),
                ('prod_base_url', models.URLField(blank=True, help_text='Production BankOne API base URL', null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=300)),
                ('bank_customer_id', models.CharField(blank=True, max_length=200, null=True)),
                ('active', models.BooleanField(default=False)),
                ('address', models.CharField(max_length=500)),
                ('email', models.EmailField(max_length=254)),
                ('phone_number', models.CharField(max_length=50)),
                ('website', models.CharField(blank=True, max_length=300, null=True)),
                ('tax_number', models.TextField(blank=True, null=True)),
                ('registration_number', models.TextField(blank=True, null=True)),
                ('registration_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CompanyAccount',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account_number', models.CharField(blank=True, max_length=20, null=True)),
                ('bank_one_account_number', models.CharField(blank=True, max_length=100, null=True)),
                ('product_code', models.CharField(blank=True, max_length=200, null=True)),
                ('active', models.BooleanField(default=True)),
                ('account_officer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.accountofficer')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('bank_customer_id', models.CharField(blank=True, max_length=200, null=True)),
                ('other_name', models.CharField(blank=True, max_length=100, null=True)),
                ('address', models.CharField(blank=True, max_length=100, null=True)),
                ('dob', models.CharField(blank=True, max_length=50, null=True)),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], default='male', max_length=50)),
                ('phone_number', models.CharField(max_length=15)),
                ('state_of_origin', models.CharField(max_length=300)),
                ('bvn_number', models.TextField()),
                ('nin_number', models.TextField()),
                ('approval_pin', models.CharField(blank=True, max_length=200, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='profile_picture')),
                ('active', models.BooleanField(default=False)),
                ('account_officer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.accountofficer')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CompanyCreationRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_email', models.EmailField(max_length=254)),
                ('business_name', models.CharField(max_length=200)),
                ('business_phone_number', models.CharField(max_length=50)),
                ('business_address', models.CharField(max_length=250)),
                ('business_website', models.CharField(blank=True, max_length=300, null=True)),
                ('business_sector', models.CharField(max_length=150)),
                ('business_tax_number', models.TextField(blank=True, null=True)),
                ('business_registration_number', models.TextField(blank=True, null=True)),
                ('business_registration_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('approved', 'Approved'), ('declined', 'Declined'), ('pending', 'Pending')], default='pending', max_length=50)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_approved_by', to=settings.AUTH_USER_MODEL)),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_checked_by', to=settings.AUTH_USER_MODEL)),
                ('rejected_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_rejected_by', to=settings.AUTH_USER_MODEL)),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_verified_by', to=settings.AUTH_USER_MODEL)),
                ('account_opened_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.customer')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
