from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction

from account.models import Customer, CompanyAccount
from bowenmfb.modules.utils import create_in_app_notification


@receiver(post_save, sender=Customer)
def notify_new_user_signup(sender, instance, created, **kwargs):
    """Create in-app notification when a new user signs up in a company."""
    if created and instance.company:
        transaction.on_commit(lambda: create_in_app_notification(
            company=instance.company,
            title="New User Signup",
            message=f"A new user {instance.user.get_full_name()} has signed up for your company.",
            notification_type='user_signup',
            related_object_id=instance.id,
            related_object_type='Customer'
        ))


@receiver(pre_save, sender=CompanyAccount)
def track_transfer_limit_changes(sender, instance, **kwargs):
    """Track transfer limit changes before saving."""
    if instance.pk:
        try:
            old_instance = CompanyAccount.objects.get(pk=instance.pk)
            instance._old_daily_limit = old_instance.daily_transfer_limit
            instance._old_monthly_limit = old_instance.monthly_transfer_limit
        except CompanyAccount.DoesNotExist:
            instance._old_daily_limit = None
            instance._old_monthly_limit = None
    else:
        instance._old_daily_limit = None
        instance._old_monthly_limit = None


@receiver(post_save, sender=CompanyAccount)
def notify_transfer_limit_update(sender, instance, created, **kwargs):
    """Create in-app notification when transfer limits are updated."""
    if not created and instance.company:
        # Check if limits have changed
        daily_changed = (hasattr(instance, '_old_daily_limit') and
                        instance._old_daily_limit is not None and
                        instance._old_daily_limit != instance.daily_transfer_limit)

        monthly_changed = (hasattr(instance, '_old_monthly_limit') and
                          instance._old_monthly_limit is not None and
                          instance._old_monthly_limit != instance.monthly_transfer_limit)

        if daily_changed or monthly_changed:
            changes = []
            if daily_changed:
                changes.append(f"Daily limit: ₦{instance._old_daily_limit:,.2f} → ₦{instance.daily_transfer_limit:,.2f}")
            if monthly_changed:
                changes.append(f"Monthly limit: ₦{instance._old_monthly_limit:,.2f} → ₦{instance.monthly_transfer_limit:,.2f}")

            message = f"Transfer limits have been updated for your account. Changes: {'; '.join(changes)}"

            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="Transfer Limits Updated",
                message=message,
                notification_type='transfer_limit_updated',
                related_object_id=instance.id,
                related_object_type='CompanyAccount'
            ))
