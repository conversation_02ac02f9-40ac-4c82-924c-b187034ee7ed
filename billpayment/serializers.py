from rest_framework import serializers
from django.utils import timezone

from bowenmfb.modules.choices import BILL_PAYMENT_BENEFICIARY_TYPE_CHOICES
from bowenmfb.modules.exceptions import InvalidRequestException
from .models import (
    BillerCategory, <PERSON>er, PaymentItem, BillPaymentRequest,
    BillPayment, BillPaymentApprovalWorkflow, BillPaymentBeneficiary
)
from account.models import Customer, CompanyAccount


class BillerCategorySerializer(serializers.ModelSerializer):
    """Serializer for BillerCategory model."""
    
    class Meta:
        model = BillerCategory
        fields = ['id', 'category_id', 'name', 'description', 'is_airtime']


class BillerSerializer(serializers.ModelSerializer):
    """Serializer for Biller model."""
    category_name = serializers.CharField(source='category.name', read_only=True)
    
    class Meta:
        model = Biller
        fields = [
            'id', 'biller_id', 'category', 'category_name', 'name', 'short_name',
            'narration', 'currency_code', 'currency_symbol', 'customer_field1',
            'customer_field2', 'support_email', 'surcharge', 'logo_url', 'is_active'
        ]


class PaymentItemSerializer(serializers.ModelSerializer):
    """Serializer for PaymentItem model."""
    biller_name = serializers.CharField(source='biller.name', read_only=True)
    amount = serializers.SerializerMethodField()

    def get_amount(self, obj):
        return float(obj.amount / 100)
    
    class Meta:
        model = PaymentItem
        fields = ['id', 'item_id', 'biller', 'biller_name', 'code', 'name', 'amount']


class BillPaymentRequestSerializerIn(serializers.ModelSerializer):
    """Input serializer for creating bill payment requests."""
    
    class Meta:
        model = BillPaymentRequest
        fields = [
            'from_account', 'biller', 'payment_item', 'amount', 'customer_id',
            'customer_name', 'customer_email', 'customer_phone', 'description'
        ]

    def validate_amount(self, value):
        """Validate that amount is positive."""
        if value <= 0:
            raise InvalidRequestException({"detail": "Amount must be greater than zero."})
        return value

    def validate_from_account(self, value):
        """Validate that the user has access to the account."""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            try:
                customer = request.user.customer
                if value.company != customer.company:
                    raise InvalidRequestException({"detail": "You don't have access to this account."})
            except:
                raise InvalidRequestException({"detail": "Invalid user or account."})
        return value

    def create(self, validated_data):
        """Create bill payment request with creator information."""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            try:
                validated_data['created_by'] = request.user.customer
                validated_data['company'] = request.user.customer.company
            except:
                pass
        return super().create(validated_data)


class BillPaymentRequestSerializerOut(serializers.ModelSerializer):
    """Output serializer for bill payment requests."""
    biller_name = serializers.CharField(source='biller.name', read_only=True)
    payment_item_name = serializers.CharField(source='payment_item.name', read_only=True)
    from_account_number = serializers.CharField(source='from_account.account_number', read_only=True)
    created_by_name = serializers.CharField(source='created_by.user.get_full_name', read_only=True)
    checked_by_name = serializers.CharField(source='checked_by.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    declined_by_name = serializers.CharField(source='declined_by.user.get_full_name', read_only=True)
    
    class Meta:
        model = BillPaymentRequest
        fields = [
            'id', 'company', 'from_account', 'from_account_number', 'biller', 'biller_name',
            'payment_item', 'payment_item_name', 'amount', 'customer_id', 'customer_name',
            'customer_email', 'customer_phone', 'description', 'created_by', 'created_by_name',
            'is_checked', 'checked_at', 'checked_by', 'checked_by_name', 'is_verified',
            'verified_at', 'verified_by', 'verified_by_name', 'is_approved', 'approved_at',
            'approved_by', 'approved_by_name', 'is_declined', 'declined_at', 'declined_by',
            'declined_by_name', 'decline_reason', 'created_on', 'updated_on'
        ]


class BillPaymentSerializer(serializers.ModelSerializer):
    """Serializer for BillPayment model."""
    from_account_number = serializers.CharField(source='from_account.account_number', read_only=True)
    
    class Meta:
        model = BillPayment
        fields = [
            'id', 'company', 'payment_request', 'from_account', 'from_account_number',
            'biller_name', 'biller_category_name', 'payment_item_name', 'customer_id',
            'customer_name', 'amount', 'fee', 'status', 'reference', 'provider_reference',
            'session_id', 'created_on', 'updated_on'
        ]


class BillPaymentApprovalWorkflowSerializerOut(serializers.ModelSerializer):
    """Output serializer for bill payment approval workflow."""
    uploaded_by_name = serializers.CharField(source='uploaded_by.user.get_full_name', read_only=True)
    checked_by_name = serializers.CharField(source='checked_by.user.get_full_name', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.user.get_full_name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.user.get_full_name', read_only=True)
    declined_by_name = serializers.CharField(source='declined_by.user.get_full_name', read_only=True)
    
    class Meta:
        model = BillPaymentApprovalWorkflow
        fields = [
            'id', 'bill_payment_request', 'current_level', 'status', 'uploaded_by',
            'uploaded_by_name', 'uploaded_at', 'checked_by', 'checked_by_name', 'checked_at',
            'verified_by', 'verified_by_name', 'verified_at', 'approved_by', 'approved_by_name',
            'approved_at', 'declined_by', 'declined_by_name', 'declined_at', 'decline_reason'
        ]


class ProcessBillPaymentApprovalSerializerIn(serializers.Serializer):
    """Input serializer for processing bill payment approval actions."""
    action = serializers.ChoiceField(choices=['check', 'verify', 'approve', 'decline'])
    decline_reason = serializers.CharField(required=False, allow_blank=True, max_length=500)
    transaction_pin = serializers.CharField()

    def validate(self, data):
        """Validate that decline_reason is provided when action is decline."""
        if data.get('action') == 'decline' and not data.get('decline_reason'):
            raise InvalidRequestException({
                'detail': 'Decline reason is required when declining a bill payment.'
            })
        transaction_pin = data.get('transaction_pin')
        if not (str(transaction_pin).isnumeric() or len(transaction_pin) == 4):
            raise InvalidRequestException({
                'detail': 'Transaction PIN must be four (4) digits'
            })
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            try:
                customer = request.user.customer
                decrypted_pin = str(customer.get_decrypted_approval_pin)
                if decrypted_pin != transaction_pin:
                    customer.failed_pin_retries += 1
                    customer.save()
                    raise InvalidRequestException({'detail': 'Incorrect Transaction PIN'})
            except:
                raise InvalidRequestException({'detail': 'Incorrect Transaction PIN'})
        return data


# BankOne API Response Serializers
class BankOneBillerCategorySerializer(serializers.Serializer):
    """Serializer for BankOne biller category API response."""
    ID = serializers.CharField()
    Name = serializers.CharField()
    Description = serializers.CharField(required=False, allow_blank=True)
    IsAirtime = serializers.BooleanField()


class BankOneBillerSerializer(serializers.Serializer):
    """Serializer for BankOne biller API response."""
    ID = serializers.CharField()
    CategoryId = serializers.CharField()
    Name = serializers.CharField()
    ShortName = serializers.CharField(required=False, allow_blank=True)
    Narration = serializers.CharField(required=False, allow_blank=True)
    CurrencyCode = serializers.CharField()
    CurrencySymbol = serializers.CharField()
    CustomerField1 = serializers.CharField(required=False, allow_blank=True)
    CustomerField2 = serializers.CharField(required=False, allow_blank=True)
    SupportEmail = serializers.CharField(required=False, allow_blank=True)
    Surcharge = serializers.FloatField()
    LogoUrl = serializers.CharField(required=False, allow_blank=True)
    IsActive = serializers.BooleanField()


class BankOnePaymentItemSerializer(serializers.Serializer):
    """Serializer for BankOne payment item API response."""
    ID = serializers.CharField()
    BillerId = serializers.CharField()
    Code = serializers.CharField()
    Name = serializers.CharField()
    Amount = serializers.FloatField()


class BankOneBillPaymentResponseSerializer(serializers.Serializer):
    """Serializer for BankOne bill payment API response."""
    Status = serializers.CharField()
    StatusDescription = serializers.CharField()
    ReferenceID = serializers.IntegerField()
    UniqueIdentifier = serializers.CharField(required=False, allow_null=True)
    IsSuccessFul = serializers.BooleanField()
    ResponseMessage = serializers.CharField()
    ResponseCode = serializers.CharField()
    Reference = serializers.CharField()
    SessionID = serializers.CharField()
    RequestStatus = serializers.BooleanField()
    ResponseDescription = serializers.CharField()
    ResponseStatus = serializers.CharField(required=False, allow_null=True)


class BillPaymentBeneficiarySerializerOut(serializers.ModelSerializer):
    """Serializer for BillPaymentBeneficiary model."""
    class Meta:
        model = BillPaymentBeneficiary
        fields = [
            'id', 'company', 'beneficiary_type', 'customer_id', 'customer_name',
            'customer_email', 'customer_phone'
        ]


class BillPaymentBeneficiarySerializerIn(serializers.Serializer):
    """Serializer for BillPaymentBeneficiary model."""
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    beneficiary_type = serializers.ChoiceField(choices=BILL_PAYMENT_BENEFICIARY_TYPE_CHOICES)
    customer_id = serializers.CharField()
    customer_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    customer_email = serializers.EmailField(required=False, allow_blank=True)
    customer_phone = serializers.CharField(required=False, allow_blank=True)

    def create(self, validated_data):
        user = validated_data.get("user")
        beneficiary_type = validated_data.get("beneficiary_type")
        customer_id = validated_data.get("customer_id")
        customer_name = validated_data.get("customer_name")
        customer_email = validated_data.get("customer_email")
        customer_phone = validated_data.get("customer_phone")

        company = user.customer.company

        try:
            BillPaymentBeneficiary.objects.get(company=company, customer_id=customer_id, beneficiary_type=beneficiary_type)
            raise InvalidRequestException({"detail": "Beneficiary already exist"})
        except BillPaymentBeneficiary.DoesNotExist:
            pass

        beneficiary = BillPaymentBeneficiary.objects.create(
            company=company, beneficiary_type=beneficiary_type, customer_id=customer_id,
            customer_name=customer_name, customer_email=customer_email, customer_phone=customer_phone
        )

        return {"detail": "Beneficiary added successfully", "data": BillPaymentBeneficiarySerializerOut(beneficiary).data}

