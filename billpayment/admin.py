from django.contrib import admin
from .models import (
    <PERSON>er<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, PaymentItem, BillPaymentRequest,
    BillPayment, BillPaymentApprovalWorkflow
)


@admin.register(BillerCategory)
class BillerCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'category_id', 'is_airtime', 'created_on']
    list_filter = ['is_airtime', 'created_on']
    search_fields = ['name', 'category_id', 'description']
    readonly_fields = ['id', 'created_on', 'updated_on']


@admin.register(Biller)
class BillerAdmin(admin.ModelAdmin):
    list_display = ['name', 'biller_id', 'category', 'currency_symbol', 'surcharge', 'is_active', 'created_on']
    list_filter = ['category', 'currency_symbol', 'is_active', 'created_on']
    search_fields = ['name', 'biller_id', 'short_name']
    readonly_fields = ['id', 'created_on', 'updated_on']
    list_editable = ['is_active']


@admin.register(PaymentItem)
class PaymentItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'item_id', 'biller', 'code', 'amount', 'created_on']
    list_filter = ['biller', 'created_on']
    search_fields = ['name', 'item_id', 'code', 'biller__name']
    readonly_fields = ['id', 'created_on', 'updated_on']


@admin.register(BillPaymentRequest)
class BillPaymentRequestAdmin(admin.ModelAdmin):
    list_display = [
        'customer_name', 'biller', 'amount', 'is_checked', 'is_verified',
        'is_approved', 'is_declined', 'created_by', 'created_on'
    ]
    list_filter = [
        'is_checked', 'is_verified', 'is_approved', 'is_declined',
        'biller', 'company', 'created_on'
    ]
    search_fields = [
        'customer_name', 'customer_id', 'customer_email', 'customer_phone',
        'biller__name', 'created_by__user__first_name', 'created_by__user__last_name'
    ]
    readonly_fields = [
        'id', 'created_on', 'updated_on', 'checked_at', 'verified_at',
        'approved_at', 'declined_at', 'provider_response'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('company', 'from_account', 'created_by')
        }),
        ('Bill Payment Details', {
            'fields': ('biller', 'payment_item', 'amount', 'customer_id', 'customer_name',
                      'customer_email', 'customer_phone', 'description')
        }),
        ('Approval Status', {
            'fields': (
                ('is_checked', 'checked_by', 'checked_at'),
                ('is_verified', 'verified_by', 'verified_at'),
                ('is_approved', 'approved_by', 'approved_at'),
                ('is_declined', 'declined_by', 'declined_at', 'decline_reason')
            )
        }),
        ('System Information', {
            'fields': ('provider_response', 'created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )


@admin.register(BillPayment)
class BillPaymentAdmin(admin.ModelAdmin):
    list_display = [
        'customer_name', 'biller_name', 'amount', 'status', 'reference',
        'provider_reference', 'created_on'
    ]
    list_filter = ['status', 'biller_category_name', 'company', 'created_on']
    search_fields = [
        'customer_name', 'customer_id', 'biller_name', 'reference',
        'provider_reference', 'session_id'
    ]
    readonly_fields = [
        'id', 'created_on', 'updated_on', 'provider_response'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('company', 'payment_request', 'from_account')
        }),
        ('Bill Payment Details', {
            'fields': ('biller_name', 'biller_category_name', 'payment_item_name',
                      'customer_id', 'customer_name', 'amount', 'fee')
        }),
        ('Transaction Details', {
            'fields': ('status', 'reference', 'provider_reference', 'session_id')
        }),
        ('System Information', {
            'fields': ('provider_response', 'created_on', 'updated_on'),
            'classes': ('collapse',)
        })
    )


@admin.register(BillPaymentApprovalWorkflow)
class BillPaymentApprovalWorkflowAdmin(admin.ModelAdmin):
    list_display = [
        'bill_payment_request', 'current_level', 'status', 'uploaded_by',
        'checked_by', 'verified_by', 'approved_by', 'uploaded_at'
    ]
    list_filter = ['current_level', 'status', 'uploaded_at']
    search_fields = [
        'bill_payment_request__customer_name', 'uploaded_by__user__first_name',
        'uploaded_by__user__last_name', 'checked_by__user__first_name',
        'verified_by__user__first_name', 'approved_by__user__first_name'
    ]
    readonly_fields = [
        'id', 'uploaded_at', 'checked_at', 'verified_at', 'approved_at',
        'declined_at', 'process_response'
    ]
    fieldsets = (
        ('Workflow Information', {
            'fields': ('bill_payment_request', 'current_level', 'status')
        }),
        ('Approval Timeline', {
            'fields': (
                ('uploaded_by', 'uploaded_at'),
                ('checked_by', 'checked_at'),
                ('verified_by', 'verified_at'),
                ('approved_by', 'approved_at'),
                ('declined_by', 'declined_at', 'decline_reason')
            )
        }),
        ('System Information', {
            'fields': ('process_response',),
            'classes': ('collapse',)
        })
    )
