import logging
from celery import shared_task
from django.conf import settings
from django.utils import timezone
from decimal import Decimal

from .models import BillPaymentRequest, BillPayment
from bowenmfb.modules.bankone import get_bankone_client
from bowenmfb.modules.exceptions import BankOneAPIError

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def process_bill_payment_task(self, bill_payment_request_id):
    """
    Celery task to process approved bill payment requests.
    
    Args:
        bill_payment_request_id: ID of the approved BillPaymentRequest
    """
    try:
        # Get the bill payment request
        bill_payment_request = BillPaymentRequest.objects.get(id=bill_payment_request_id)
        
        if not bill_payment_request.is_approved:
            logger.error(f"Bill payment request {bill_payment_request_id} is not approved")
            return {'success': False, 'error': 'Bill payment request is not approved'}
        
        # Check if already processed
        if BillPayment.objects.filter(payment_request=bill_payment_request).exists():
            logger.info(f"Bill payment request {bill_payment_request_id} already processed")
            return {'success': True, 'message': 'Bill payment already processed'}
        
        # Initialize BankOne client
        client = get_bankone_client()
        
        # Prepare bill payment data
        payment_data = {
            'biller_id': bill_payment_request.biller.biller_id,
            'amount': float(bill_payment_request.amount),
            'biller_name': bill_payment_request.biller.name,
            'biller_category_id': bill_payment_request.biller.category.category_id,
            'biller_category_name': bill_payment_request.biller.category.name,
            'payment_item_id': bill_payment_request.payment_item.item_id if bill_payment_request.payment_item else "01",
            'payment_item_name': bill_payment_request.payment_item.name if bill_payment_request.payment_item else bill_payment_request.biller.name,
            'customer_id': bill_payment_request.customer_id,
            'customer_deposit_slip_number': f"BP{bill_payment_request.id}{int(timezone.now().timestamp())}",
            'customer_name': bill_payment_request.customer_name,
            'account_number': bill_payment_request.from_account.account_number,
            'customer_email': bill_payment_request.customer_email or bill_payment_request.created_by.user.email,
            'customer_phone': bill_payment_request.customer_phone or bill_payment_request.created_by.phone_number
        }
        
        # Call BankOne API to process bill payment
        logger.info(f"Processing bill payment for request {bill_payment_request_id}")
        response = client.initialize_bill_payment(**payment_data)
        
        # Create BillPayment record
        bill_payment = BillPayment.objects.create(
            company=bill_payment_request.company,
            payment_request=bill_payment_request,
            from_account=bill_payment_request.from_account,
            biller_name=bill_payment_request.biller.name,
            biller_category_name=bill_payment_request.biller.category.name,
            payment_item_name=bill_payment_request.payment_item.name if bill_payment_request.payment_item else "",
            customer_id=bill_payment_request.customer_id,
            customer_name=bill_payment_request.customer_name,
            amount=bill_payment_request.amount,
            fee=0.0,  # Fee can be calculated based on biller surcharge
            reference=payment_data['customer_deposit_slip_number'],
            provider_reference=response.get('Reference', ''),
            session_id=response.get('SessionID', ''),
            provider_response=str(response)
        )

        if settings.DEBUG:
            response["IsSuccessFul"] = True
            response["ResponseCode"] = "00"
        
        # Update status based on response
        if response.get('IsSuccessFul', False) and response.get('ResponseCode') == '00':
            bill_payment.status = 'success'
            logger.info(f"Bill payment {bill_payment.id} processed successfully")
        else:
            bill_payment.status = 'failed'
            logger.error(f"Bill payment {bill_payment.id} failed: {response.get('ResponseMessage', 'Unknown error')}")
        
        bill_payment.save()
        
        # Update the bill payment request with provider response
        bill_payment_request.provider_response = str(response)
        bill_payment_request.save()
        
        return {
            'success': True,
            'bill_payment_id': bill_payment.id,
            'status': bill_payment.status,
            'reference': bill_payment.reference,
            'provider_reference': bill_payment.provider_reference
        }
        
    except BillPaymentRequest.DoesNotExist:
        error_msg = f"Bill payment request {bill_payment_request_id} not found"
        logger.error(error_msg)
        return {'success': False, 'error': error_msg}
    
    except BankOneAPIError as e:
        error_msg = f"BankOne API error for bill payment request {bill_payment_request_id}: {str(e)}"
        logger.error(error_msg)
        
        # Create failed bill payment record
        try:
            bill_payment_request = BillPaymentRequest.objects.get(id=bill_payment_request_id)
            BillPayment.objects.create(
                company=bill_payment_request.company,
                payment_request=bill_payment_request,
                from_account=bill_payment_request.from_account,
                biller_name=bill_payment_request.biller.name,
                biller_category_name=bill_payment_request.biller.category.name,
                payment_item_name=bill_payment_request.payment_item.name if bill_payment_request.payment_item else "",
                customer_id=bill_payment_request.customer_id,
                customer_name=bill_payment_request.customer_name,
                amount=bill_payment_request.amount,
                status='failed',
                provider_response=str(e)
            )
        except:
            pass
        
        # Retry the task if retries are available
        # if self.request.retries < self.max_retries:
        #     logger.info(f"Retrying bill payment task for request {bill_payment_request_id} (attempt {self.request.retries + 1})")
        #     raise self.retry(countdown=60 * (self.request.retries + 1))  # Exponential backoff
        #
        # return {'success': False, 'error': error_msg}
    
    except Exception as e:
        error_msg = f"Unexpected error processing bill payment request {bill_payment_request_id}: {str(e)}"
        logger.error(error_msg)
        
        # Create failed bill payment record
        try:
            bill_payment_request = BillPaymentRequest.objects.get(id=bill_payment_request_id)
            BillPayment.objects.create(
                company=bill_payment_request.company,
                payment_request=bill_payment_request,
                from_account=bill_payment_request.from_account,
                biller_name=bill_payment_request.biller.name,
                biller_category_name=bill_payment_request.biller.category.name,
                payment_item_name=bill_payment_request.payment_item.name if bill_payment_request.payment_item else "",
                customer_id=bill_payment_request.customer_id,
                customer_name=bill_payment_request.customer_name,
                amount=bill_payment_request.amount,
                status='failed',
                provider_response=str(e)
            )
        except:
            pass
        
        # Retry the task if retries are available
        # if self.request.retries < self.max_retries:
        #     logger.info(f"Retrying bill payment task for request {bill_payment_request_id} (attempt {self.request.retries + 1})")
        #     raise self.retry(countdown=60 * (self.request.retries + 1))  # Exponential backoff

        return {'success': False, 'error': error_msg}


@shared_task
def sync_biller_data_task():
    """
    Celery task to periodically sync biller data from BankOne API.
    This can be run as a periodic task to keep biller data up to date.
    """
    try:
        from .views import sync_biller_categories, sync_billers, sync_payment_items
        from django.http import HttpRequest
        from django.contrib.auth.models import AnonymousUser
        
        # Create a mock request for the sync functions
        request = HttpRequest()
        request.user = AnonymousUser()
        
        logger.info("Starting periodic sync of biller data")
        
        # Sync categories
        categories_result = sync_biller_categories(request)
        logger.info(f"Categories sync result: {categories_result.data}")
        
        # Sync billers
        billers_result = sync_billers(request)
        logger.info(f"Billers sync result: {billers_result.data}")
        
        # Sync payment items
        items_result = sync_payment_items(request)
        logger.info(f"Payment items sync result: {items_result.data}")
        
        return {
            'success': True,
            'categories': categories_result.data,
            'billers': billers_result.data,
            'payment_items': items_result.data
        }
        
    except Exception as e:
        error_msg = f"Error during periodic biller data sync: {str(e)}"
        logger.error(error_msg)
        return {'success': False, 'error': error_msg}
