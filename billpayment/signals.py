"""
Django signals for bill payment notifications.

This module handles sending notifications when bill payment request statuses change.
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction

from bowenmfb.modules.utils import send_notification_async, log_request, create_in_app_notification
from .models import BillPaymentRequest


@receiver(pre_save, sender=BillPaymentRequest)
def track_bill_payment_status_changes(sender, instance, **kwargs):
    """Track status changes for bill payment requests before saving."""
    if instance.pk:
        try:
            # Get the previous state from database
            old_instance = BillPaymentRequest.objects.get(pk=instance.pk)
            instance._old_is_checked = old_instance.is_checked
            instance._old_is_verified = old_instance.is_verified
            instance._old_is_approved = old_instance.is_approved
            instance._old_is_declined = old_instance.is_declined
        except BillPaymentRequest.DoesNotExist:
            instance._old_is_checked = False
            instance._old_is_verified = False
            instance._old_is_approved = False
            instance._old_is_declined = False
    else:
        instance._old_is_checked = False
        instance._old_is_verified = False
        instance._old_is_approved = False
        instance._old_is_declined = False


@receiver(post_save, sender=BillPaymentRequest)
def send_bill_payment_notifications(sender, instance, created, **kwargs):
    """Send notifications when bill payment request status changes."""
    if created:
        # Create in-app notification for new bill payment request
        if instance.company:
            biller_name = instance.biller.name if instance.biller else "Unknown Biller"
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="New Bill Payment Request Created",
                message=f"A new bill payment request of ₦{instance.amount:,.2f} to {biller_name} for {instance.customer_name} has been created.",
                notification_type='bill_payment_created',
                related_object_id=instance.id,
                related_object_type='BillPaymentRequest'
            ))
        return
    
    # Check if any status has changed
    status_changes = []
    
    if hasattr(instance, '_old_is_checked') and not instance._old_is_checked and instance.is_checked:
        status_changes.append('checked')
    
    if hasattr(instance, '_old_is_verified') and not instance._old_is_verified and instance.is_verified:
        status_changes.append('verified')
    
    if hasattr(instance, '_old_is_approved') and not instance._old_is_approved and instance.is_approved:
        status_changes.append('approved')
    
    if hasattr(instance, '_old_is_declined') and not instance._old_is_declined and instance.is_declined:
        status_changes.append('declined')
    
    # Send notifications for each status change
    for status in status_changes:
        transaction.on_commit(lambda s=status: send_bill_payment_status_notification(instance, s))


def send_bill_payment_status_notification(bill_payment_request, status):
    """Send notification for bill payment status change."""
    try:
        if not bill_payment_request.created_by:
            return
        
        customer = bill_payment_request.created_by
        company_name = bill_payment_request.company.name if bill_payment_request.company else "Your Company"
        biller_name = bill_payment_request.biller.name if bill_payment_request.biller else "Unknown Biller"
        
        # Prepare status-specific messages
        status_messages = {
            'checked': {
                'sms': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been checked and is under review.",
                'email_subject': 'Bill Payment Request Checked',
                'email_body': f"""Dear {customer.user.get_full_name()},

Your bill payment request has been checked and is now under review.

Payment Details:
- Amount: ₦{bill_payment_request.amount:,.2f}
- Biller: {biller_name}
- Customer ID: {bill_payment_request.customer_id}
- Customer Name: {bill_payment_request.customer_name}
- Description: {bill_payment_request.description or 'N/A'}
- Company: {company_name}

Your request is now in the verification stage.

Best regards,
Bowen MFB Team"""
            },
            'verified': {
                'sms': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been verified and is awaiting final approval.",
                'email_subject': 'Bill Payment Request Verified',
                'email_body': f"""Dear {customer.user.get_full_name()},

Your bill payment request has been verified and is awaiting final approval.

Payment Details:
- Amount: ₦{bill_payment_request.amount:,.2f}
- Biller: {biller_name}
- Customer ID: {bill_payment_request.customer_id}
- Customer Name: {bill_payment_request.customer_name}
- Description: {bill_payment_request.description or 'N/A'}
- Company: {company_name}

Your request is now in the final approval stage.

Best regards,
Bowen MFB Team"""
            },
            'approved': {
                'sms': f"Great news! Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been approved and will be processed shortly.",
                'email_subject': 'Bill Payment Request Approved',
                'email_body': f"""Dear {customer.user.get_full_name()},

Great news! Your bill payment request has been approved and will be processed shortly.

Payment Details:
- Amount: ₦{bill_payment_request.amount:,.2f}
- Biller: {biller_name}
- Customer ID: {bill_payment_request.customer_id}
- Customer Name: {bill_payment_request.customer_name}
- Description: {bill_payment_request.description or 'N/A'}
- Company: {company_name}

The payment will be executed and you will receive a confirmation once completed.

Best regards,
Bowen MFB Team"""
            },
            'declined': {
                'sms': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been declined. Reason: {bill_payment_request.decline_reason or 'Not specified'}",
                'email_subject': 'Bill Payment Request Declined',
                'email_body': f"""Dear {customer.user.get_full_name()},

We regret to inform you that your bill payment request has been declined.

Payment Details:
- Amount: ₦{bill_payment_request.amount:,.2f}
- Biller: {biller_name}
- Customer ID: {bill_payment_request.customer_id}
- Customer Name: {bill_payment_request.customer_name}
- Description: {bill_payment_request.description or 'N/A'}
- Company: {company_name}

Reason for decline: {bill_payment_request.decline_reason or 'Not specified'}

Please contact your account officer for more information or to submit a new request.

Best regards,
Bowen MFB Team"""
            }
        }
        
        if status in status_messages:
            message_data = status_messages[status]
            
            # Send SMS notification
            send_notification_async(
                notification_type='sms',
                recipient=customer.phone_number,
                message=message_data['sms']
            )
            
            # Send email notification
            send_notification_async(
                notification_type='email',
                recipient=customer.user.email,
                message=message_data['email_body'],
                subject=message_data['email_subject']
            )
            
            log_request(f"Sent {status} notification for bill payment {bill_payment_request.id} to {customer.user.email}")

            # Create in-app notification
            if bill_payment_request.company:
                notification_titles = {
                    'checked': 'Bill Payment Request Checked',
                    'verified': 'Bill Payment Request Verified',
                    'approved': 'Bill Payment Request Approved',
                    'declined': 'Bill Payment Request Declined'
                }

                notification_messages = {
                    'checked': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been checked.",
                    'verified': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been verified.",
                    'approved': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been approved.",
                    'declined': f"Your bill payment of ₦{bill_payment_request.amount:,.2f} to {biller_name} for {bill_payment_request.customer_name} has been declined."
                }

                create_in_app_notification(
                    company=bill_payment_request.company,
                    title=notification_titles.get(status, 'Bill Payment Status Update'),
                    message=notification_messages.get(status, f'Bill payment status updated to {status}'),
                    notification_type=f'bill_payment_{status}',
                    related_object_id=bill_payment_request.id,
                    related_object_type='BillPaymentRequest'
                )

    except Exception as e:
        log_request(f"Failed to send bill payment {status} notification: {str(e)}")
