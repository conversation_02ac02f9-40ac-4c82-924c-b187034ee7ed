# Generated by Django 5.2.1 on 2025-07-05 16:55

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0013_remove_customer_account_officer'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillerCategory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category_id', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_airtime', models.BooleanField(default=False)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Biller Category',
                'verbose_name_plural': 'Biller Categories',
            },
        ),
        migrations.CreateModel(
            name='Biller',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('biller_id', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('short_name', models.CharField(blank=True, max_length=50, null=True)),
                ('narration', models.TextField(blank=True, null=True)),
                ('currency_code', models.CharField(default='566', max_length=10)),
                ('currency_symbol', models.CharField(default='NGN', max_length=10)),
                ('customer_field1', models.CharField(blank=True, max_length=200, null=True)),
                ('customer_field2', models.CharField(blank=True, max_length=200, null=True)),
                ('support_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('surcharge', models.FloatField(default=0.0)),
                ('logo_url', models.URLField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='billers', to='billpayment.billercategory')),
            ],
            options={
                'verbose_name': 'Biller',
                'verbose_name_plural': 'Billers',
            },
        ),
        migrations.CreateModel(
            name='BillPaymentRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.FloatField(default=1.0)),
                ('customer_id', models.CharField(help_text='Phone number, meter number, or customer ID', max_length=100)),
                ('customer_name', models.CharField(max_length=200)),
                ('customer_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('customer_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('description', models.CharField(blank=True, max_length=200, null=True)),
                ('is_checked', models.BooleanField(default=False)),
                ('checked_at', models.DateTimeField(blank=True, null=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('is_declined', models.BooleanField(default=False)),
                ('declined_at', models.DateTimeField(blank=True, null=True)),
                ('decline_reason', models.CharField(blank=True, max_length=250, null=True)),
                ('provider_response', models.TextField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bill_payment_approved_by', to='account.customer')),
                ('biller', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='billpayment.biller')),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bill_payment_checked_by', to='account.customer')),
                ('company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bill_payment_created_by', to='account.customer')),
                ('declined_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bill_payment_declined_by', to='account.customer')),
                ('from_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.companyaccount')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bill_payment_verified_by', to='account.customer')),
            ],
            options={
                'verbose_name': 'Bill Payment Request',
                'verbose_name_plural': 'Bill Payment Requests',
            },
        ),
        migrations.CreateModel(
            name='BillPaymentApprovalWorkflow',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_level', models.IntegerField(default=1)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('approved', 'Approved'), ('declined', 'Declined'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('checked_at', models.DateTimeField(blank=True, null=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('declined_at', models.DateTimeField(blank=True, null=True)),
                ('decline_reason', models.TextField(blank=True, null=True)),
                ('process_response', models.TextField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_bill_payments', to='account.customer')),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='checked_bill_payments', to='account.customer')),
                ('declined_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='declined_bill_payments', to='account.customer')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_bill_payments', to='account.customer')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_bill_payments', to='account.customer')),
                ('bill_payment_request', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='approval_workflow', to='billpayment.billpaymentrequest')),
            ],
            options={
                'verbose_name': 'Bill Payment Approval Workflow',
                'verbose_name_plural': 'Bill Payment Approval Workflows',
            },
        ),
        migrations.CreateModel(
            name='BillPayment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('biller_name', models.CharField(max_length=200)),
                ('biller_category_name', models.CharField(max_length=200)),
                ('payment_item_name', models.CharField(blank=True, max_length=200, null=True)),
                ('customer_id', models.CharField(max_length=100)),
                ('customer_name', models.CharField(max_length=200)),
                ('amount', models.FloatField(default=1.0)),
                ('fee', models.FloatField(default=0.0)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('failed', 'Failed'), ('success', 'Success')], default='pending', max_length=100)),
                ('reference', models.CharField(blank=True, max_length=50, null=True)),
                ('provider_reference', models.CharField(blank=True, max_length=200, null=True)),
                ('session_id', models.CharField(blank=True, max_length=200, null=True)),
                ('provider_response', models.TextField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
                ('from_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.companyaccount')),
                ('payment_request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='billpayment.billpaymentrequest')),
            ],
            options={
                'verbose_name': 'Bill Payment',
                'verbose_name_plural': 'Bill Payments',
            },
        ),
        migrations.CreateModel(
            name='PaymentItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('item_id', models.CharField(max_length=50)),
                ('code', models.CharField(max_length=50)),
                ('name', models.CharField(max_length=200)),
                ('amount', models.FloatField(default=0.0)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('biller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_items', to='billpayment.biller')),
            ],
            options={
                'verbose_name': 'Payment Item',
                'verbose_name_plural': 'Payment Items',
                'unique_together': {('item_id', 'biller')},
            },
        ),
        migrations.AddField(
            model_name='billpaymentrequest',
            name='payment_item',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='billpayment.paymentitem'),
        ),
    ]
