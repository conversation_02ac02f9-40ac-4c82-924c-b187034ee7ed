# Generated by Django 5.2.1 on 2025-07-06 20:30

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0013_remove_customer_account_officer'),
        ('billpayment', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillPaymentBeneficiary',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('beneficiary_type', models.CharField(choices=[('airtime', 'Airtime'), ('data', 'Data'), ('electricity', 'Electricity'), ('cable', 'Cable'), ('betting', 'Betting'), ('others', 'Others')], default='airtime', max_length=200)),
                ('customer_id', models.Char<PERSON>ield(help_text='Phone number, meter number, or customer ID', max_length=100)),
                ('customer_name', models.CharField(blank=True, max_length=200, null=True)),
                ('customer_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('customer_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.company')),
            ],
            options={
                'verbose_name': 'Bill Payment Beneficiary',
                'verbose_name_plural': 'Bill Payment Beneficiaries',
            },
        ),
    ]
