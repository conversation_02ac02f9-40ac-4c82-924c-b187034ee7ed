from django.core.exceptions import ValidationError
from django.db import models

from account.models import BaseModel, Company, CompanyAccount, Customer
from bowenmfb.modules.choices import STATUS_CHOICES, WORKFLOW_STATUS_CHOICES, TRANSFER_BENEFICIARY_TYPE_CHOICES, BILL_PAYMENT_BENEFICIARY_TYPE_CHOICES


class BillerCategory(BaseModel):
    """
    Model to store biller categories from BankOne API.
    """
    category_id = models.CharField(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    is_airtime = models.BooleanField(default=False)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Biller Category'
        verbose_name_plural = 'Biller Categories'

    def __str__(self):
        return self.name


class Biller(BaseModel):
    """
    Model to store billers from BankOne API.
    """
    biller_id = models.CharField(max_length=50, unique=True)
    category = models.ForeignKey(BillerCategory, on_delete=models.CASCADE, related_name='billers')
    name = models.CharField(max_length=200)
    short_name = models.CharField(max_length=50, blank=True, null=True)
    narration = models.TextField(blank=True, null=True)
    currency_code = models.CharField(max_length=10, default="566")
    currency_symbol = models.CharField(max_length=10, default="NGN")
    customer_field1 = models.CharField(max_length=200, blank=True, null=True)
    customer_field2 = models.CharField(max_length=200, blank=True, null=True)
    support_email = models.EmailField(blank=True, null=True)
    surcharge = models.FloatField(default=0.0)
    logo_url = models.URLField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Biller'
        verbose_name_plural = 'Billers'

    def __str__(self):
        return self.name


class PaymentItem(BaseModel):
    """
    Model to store payment items from BankOne API.
    """
    item_id = models.CharField(max_length=50)
    biller = models.ForeignKey(Biller, on_delete=models.CASCADE, related_name='payment_items')
    code = models.CharField(max_length=50)
    name = models.CharField(max_length=200)
    amount = models.FloatField(default=0.0)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Payment Item'
        verbose_name_plural = 'Payment Items'
        unique_together = ['item_id', 'biller']

    def __str__(self):
        return f"{self.biller.name} - {self.name}"


class BillPaymentRequest(BaseModel):
    """
    Model for bill payment requests that require approval workflow.
    Similar to SingleTransferRequest but for bill payments.
    """
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True)
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)

    # Bill payment details
    biller = models.ForeignKey(Biller, on_delete=models.SET_NULL, null=True)
    payment_item = models.ForeignKey(PaymentItem, on_delete=models.SET_NULL, null=True, blank=True)
    amount = models.FloatField(default=1.0)
    customer_id = models.CharField(max_length=100, help_text="Phone number, meter number, or customer ID")
    customer_name = models.CharField(max_length=200)
    customer_email = models.EmailField(blank=True, null=True)
    customer_phone = models.CharField(max_length=20, blank=True, null=True)
    description = models.CharField(max_length=200, blank=True, null=True)

    # Approval workflow fields
    created_by = models.ForeignKey(Customer, blank=True, related_name="bill_payment_created_by", null=True, on_delete=models.SET_NULL)
    is_checked = models.BooleanField(default=False)
    checked_at = models.DateTimeField(blank=True, null=True)
    checked_by = models.ForeignKey(Customer, blank=True, related_name="bill_payment_checked_by", null=True, on_delete=models.SET_NULL)
    is_verified = models.BooleanField(default=False)
    verified_at = models.DateTimeField(blank=True, null=True)
    verified_by = models.ForeignKey(Customer, blank=True, related_name="bill_payment_verified_by", null=True, on_delete=models.SET_NULL)
    is_approved = models.BooleanField(default=False)
    approved_at = models.DateTimeField(blank=True, null=True)
    approved_by = models.ForeignKey(Customer, blank=True, related_name="bill_payment_approved_by", null=True, on_delete=models.SET_NULL)
    is_declined = models.BooleanField(default=False)
    declined_at = models.DateTimeField(blank=True, null=True)
    declined_by = models.ForeignKey(Customer, blank=True, related_name="bill_payment_declined_by", null=True, on_delete=models.SET_NULL)
    decline_reason = models.CharField(max_length=250, blank=True, null=True)

    # Provider response
    provider_response = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Bill Payment Request'
        verbose_name_plural = 'Bill Payment Requests'

    def __str__(self):
        return f"{self.biller.name if self.biller else 'Unknown Biller'} - {self.customer_name} - ₦{self.amount}"


class BillPayment(BaseModel):
    """
    Model for executed bill payments.
    Similar to SingleTransfer but for bill payments.
    """
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, blank=True, null=True)
    payment_request = models.ForeignKey(BillPaymentRequest, on_delete=models.SET_NULL, blank=True, null=True)
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)

    # Bill payment details
    biller_name = models.CharField(max_length=200)
    biller_category_name = models.CharField(max_length=200)
    payment_item_name = models.CharField(max_length=200, blank=True, null=True)
    customer_id = models.CharField(max_length=100)
    customer_name = models.CharField(max_length=200)
    amount = models.FloatField(default=1.0)
    fee = models.FloatField(default=0.0)

    # Transaction details
    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default='pending')
    reference = models.CharField(max_length=50, blank=True, null=True)
    provider_reference = models.CharField(max_length=200, blank=True, null=True)
    session_id = models.CharField(max_length=200, blank=True, null=True)
    provider_response = models.TextField(blank=True, null=True)

    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Bill Payment'
        verbose_name_plural = 'Bill Payments'

    def __str__(self):
        return f"{self.biller_name} - {self.customer_name} - ₦{self.amount}"


class BillPaymentApprovalWorkflow(BaseModel):
    """
    Model to track the approval workflow for bill payment requests.
    Records who performed each action and when.
    """
    # Link to bill payment request
    bill_payment_request = models.OneToOneField(
        BillPaymentRequest,
        on_delete=models.CASCADE,
        related_name='approval_workflow'
    )

    # Workflow status
    current_level = models.IntegerField(default=1)
    status = models.CharField(max_length=20, choices=WORKFLOW_STATUS_CHOICES, default='pending')

    # Approval tracking
    uploaded_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, related_name='uploaded_bill_payments')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    checked_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='checked_bill_payments')
    checked_at = models.DateTimeField(null=True, blank=True)

    verified_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_bill_payments')
    verified_at = models.DateTimeField(null=True, blank=True)

    approved_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_bill_payments')
    approved_at = models.DateTimeField(null=True, blank=True)

    declined_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='declined_bill_payments')
    declined_at = models.DateTimeField(null=True, blank=True)
    decline_reason = models.TextField(blank=True, null=True)
    process_response = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = 'Bill Payment Approval Workflow'
        verbose_name_plural = 'Bill Payment Approval Workflows'

    @property
    def company_account(self):
        """Get the company account for this bill payment"""
        return self.bill_payment_request.from_account if self.bill_payment_request else None

    def can_user_perform_action(self, user, action):
        """
        Check if a user can perform a specific action on this workflow.
        Actions: 'check', 'verify', 'approve', 'decline'
        """
        try:
            from transfer.models import AccountSignatory, SignatoryHierarchy

            customer = user.customer
            account = self.company_account
            if not account:
                return False

            signatory = AccountSignatory.objects.filter(
                company_account=account,
                customer=customer,
                is_active=True
            ).first()

            if not signatory:
                return False

            # Check if single signatory account
            hierarchy = SignatoryHierarchy.objects.filter(company_account=account).first()
            if hierarchy and hierarchy.is_single_signatory:
                return True

            if action == 'check':
                return signatory.can_check and self.current_level == 2
            elif action == 'verify':
                return signatory.can_verify and self.current_level == 3
            elif action == 'approve':
                return signatory.can_approve and self.current_level >= 4
            elif action == 'decline':
                return True  # Any signatory can decline

            return False
        except:
            return False

    def __str__(self):
        return f"Bill Payment Workflow - Level {self.current_level} ({self.status})"


class BillPaymentBeneficiary(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    beneficiary_type = models.CharField(max_length=200, choices=BILL_PAYMENT_BENEFICIARY_TYPE_CHOICES, default='airtime')
    customer_id = models.CharField(max_length=100, help_text="Phone number, meter number, or customer ID")
    customer_name = models.CharField(max_length=200, blank=True, null=True)
    customer_email = models.EmailField(blank=True, null=True)
    customer_phone = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        verbose_name = 'Bill Payment Beneficiary'
        verbose_name_plural = 'Bill Payment Beneficiaries'

    def __str__(self):
        return f"{self.company.name}: {self.customer_id}"



