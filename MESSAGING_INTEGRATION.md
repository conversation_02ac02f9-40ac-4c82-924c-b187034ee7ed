# BankOne Messaging Integration

This document describes the new messaging functionality added to the BankOne client and the notification system.

## Overview

Two new endpoints have been added to the BankOneClient for sending SMS and email notifications:

1. **Bulk SMS**: Send SMS messages to multiple recipients
2. **Email**: Send email messages to multiple recipients

Additionally, a Celery task system has been implemented that automatically chooses between tmsaas (for DEBUG mode) and BankOne API (for production) for sending notifications.

## BankOne API Endpoints

### 1. Bulk SMS Endpoint

**URL**: `https://staging.mybankone.com/BankOneWebAPI/api/Messaging/SaveBulkSms/2?authToken={token}`

**Method**: POST

**Payload**:
```json
[
    {
        "AccountNumber": "**********",
        "To": "***********",
        "Body": "Test sms message",
        "ReferenceNo": "SMS113"
    }
]
```

**Response**:
```json
{
    "Status": true,
    "ErrorMessage": ""
}
```

### 2. Email Endpoint

**URL**: `https://staging.mybankone.com/BankOneWebAPI/api/Messaging/SaveEmail/2?authToken={token}`

**Method**: POST

**Payload**:
```json
[
    {
        "InstitutionCode": "223333",
        "MfbCode": "123422",
        "emailFrom": "<EMAIL>",
        "emailTo": "<EMAIL>",
        "Subject": "Email Subject",
        "Message": "This is test email content"
    }
]
```

**Response**:
```json
{
    "Status": true,
    "ErrorMessage": ""
}
```

## Usage Examples

### Direct BankOne API Usage

```python
from bowenmfb.modules.bankone import BankOneClient

client = BankOneClient()

# Send SMS
sms_messages = [{
    "AccountNumber": "**********",
    "To": "***********",
    "Body": "Your balance is N50,000",
    "ReferenceNo": "SMS001"
}]
response = client.send_bulk_sms(sms_messages)

# Send Email
email_messages = [{
    "InstitutionCode": "223333",
    "MfbCode": "123422",
    "emailFrom": "<EMAIL>",
    "emailTo": "<EMAIL>",
    "Subject": "Account Statement",
    "Message": "Your statement is ready"
}]
response = client.send_email(email_messages)
```

### Asynchronous Notifications with Celery

```python
from superadmin.tasks import send_notification_task

# Send SMS (uses tmsaas in DEBUG mode, BankOne in production)
task = send_notification_task.delay(
    notification_type='sms',
    recipient='***********',
    message='Your OTP is 123456',
    account_number='**********',
    reference_no='OTP001'
)

# Send Email (uses tmsaas in DEBUG mode, BankOne in production)
task = send_notification_task.delay(
    notification_type='email',
    recipient='<EMAIL>',
    message='Welcome to Bowen MFB!',
    subject='Welcome',
    institution_code='223333',
    mfb_code='123422',
    email_from='<EMAIL>'
)
```

### Helper Function (Recommended)

```python
from bowenmfb.modules.utils import send_notification_async

# Send SMS with auto-generated reference
task = send_notification_async(
    notification_type='sms',
    recipient='***********',
    message='Transaction successful'
)

# Send Email with defaults
task = send_notification_async(
    notification_type='email',
    recipient='<EMAIL>',
    message='Your statement is ready',
    subject='Monthly Statement'
)
```

## Configuration

### Required Settings

For production (BankOne API), ensure these are configured in your BankConstantTable:
- `auth_token`: BankOne authentication token
- `base_url`: BankOne API base URL

For DEBUG mode (tmsaas), ensure these settings exist:
- `TMSAAS_BASE_URL`
- `TMSAAS_CLIENT_ID`
- `TMSAAS_CLIENT_SECRET`
- `TMSAAS_SENDER_ID`

### Optional Settings

```python
# Default SMS account number (if not provided)
DEFAULT_SMS_ACCOUNT_NUMBER = '**********'

# Default email settings for BankOne
DEFAULT_INSTITUTION_CODE = '223333'
DEFAULT_MFB_CODE = '123422'
DEFAULT_EMAIL_FROM = '<EMAIL>'
```

## Behavior in Different Environments

### DEBUG = True
- Uses tmsaas functions (`send_tmsaas_sms`, `send_tmsaas_email`)
- Faster for development and testing
- Requires tmsaas configuration

### DEBUG = False (Production)
- Uses BankOne API endpoints
- Requires BankOne authentication token
- Full integration with core banking system

## Error Handling

The notification task returns a standardized response:

```python
{
    'success': True/False,
    'service': 'tmsaas' or 'bankone',
    'type': 'sms' or 'email',
    'recipient': 'phone_or_email',
    'response': {...},  # Service response
    'error': 'error_message'  # Only if success=False
}
```

## Files Modified/Created

1. **bowenmfb/modules/bankone.py**: Added `send_bulk_sms()` and `send_email()` methods
2. **superadmin/tasks.py**: Added `send_notification_task()` Celery task
3. **bowenmfb/modules/utils.py**: Added `send_notification_async()` helper function
4. **bowenmfb/modules/notification_examples.py**: Usage examples
5. **bowenmfb/modules/bankone_examples.py**: Updated with messaging examples
6. **bowenmfb/modules/test_bankone.py**: Updated tests to include new methods

## Testing

Run the examples:
```bash
python bowenmfb/modules/notification_examples.py
```

Test BankOne client methods:
```bash
python bowenmfb/modules/test_bankone.py
```

## Best Practices

1. **Use the helper function** (`send_notification_async`) for most use cases
2. **Handle task results** appropriately in your application
3. **Set appropriate timeouts** for task execution
4. **Log notification attempts** for audit purposes
5. **Validate recipient formats** before sending
6. **Use meaningful reference numbers** for SMS messages

## Security Considerations

1. **Protect authentication tokens** - store securely in database
2. **Validate input data** before sending notifications
3. **Rate limit** notification sending to prevent abuse
4. **Log all notification attempts** for security auditing
5. **Use HTTPS** for all API communications
