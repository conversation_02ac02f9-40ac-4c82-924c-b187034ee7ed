amqp==5.3.1
asgiref==3.8.1
asttokens==3.0.0
attrs==25.3.0
billiard==4.2.1
celery==5.5.3
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
crypto==1.4.1
cryptography==45.0.3
decorator==5.2.1
Django==5.2.1
django-cors-headers==4.7.0
django-data-browser==4.2.10
django-debug-toolbar==5.2.0
django-decouple==2.1
django-environ==0.12.0
django-extensions==4.1
djangorestframework==3.16.0
djangorestframework_simplejwt==5.5.0
drf-spectacular==0.28.0
drf-spectacular-sidecar==2025.5.1
executing==2.2.0
gunicorn==23.0.0
hyperlink==21.0.0
idna==3.10
inflection==0.5.1
ipython==9.2.0
ipython_pygments_lexers==1.1.1
jedi==0.19.2
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kombu==5.5.4
matplotlib-inline==0.1.7
Naked==0.1.32
packaging==25.0
parso==0.8.4
pexpect==4.9.0
pillow==11.2.1
prompt_toolkit==3.0.51
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure_eval==0.2.3
pycparser==2.22
Pygments==2.19.1
PyJWT==2.9.0
python-dateutil==2.9.0.post0
PyYAML==6.0.2
redis==6.2.0
referencing==0.36.2
requests==2.32.3
rpds-py==0.25.1
shellescape==3.8.1
six==1.17.0
sqlparse==0.5.3
stack-data==0.6.3
traitlets==5.14.3
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0
vine==5.1.0
wcwidth==0.2.13
