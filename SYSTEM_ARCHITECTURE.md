# BowenMFB Corporate Banking System - Architecture Document

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Patterns](#architecture-patterns)
3. [System Components](#system-components)
4. [Database Architecture](#database-architecture)
5. [API Architecture](#api-architecture)
6. [Security Architecture](#security-architecture)
7. [Signatory Workflow Architecture](#signatory-workflow-architecture)
8. [Integration Architecture](#integration-architecture)
9. [Deployment Architecture](#deployment-architecture)

## System Overview

BowenMFB Corporate Banking System is a Django-based REST API backend that provides comprehensive banking services for corporate clients. The system integrates with BankOne core banking APIs and implements a sophisticated signatory-based approval workflow for financial transactions.

### Key Capabilities
- Corporate account management
- Multi-level signatory approval workflows
- Single and bulk transfer processing
- Real-time transaction monitoring
- Admin role-based access control (RBAC)
- BankOne API integration
- Bill payment services

### Technology Stack
- **Backend Framework**: Django 4.x with Django REST Framework
- **Database**: PostgreSQL (production & development)
- **Task Queue**: Celery with Redis
- **Authentication**: JWT (Simple JWT)
- **API Documentation**: DRF Spectacular (OpenAPI/Swagger)
- **External Integration**: BankOne Core Banking APIs

## Architecture Patterns

### 1. Layered Architecture
```
┌─────────────────────────────────────┐
│           Presentation Layer        │
│        (REST API Endpoints)         │
├─────────────────────────────────────┤
│           Business Layer            │
│     (Views, Serializers, Logic)     │
├─────────────────────────────────────┤
│            Data Layer               │
│        (Models, Database)           │
├─────────────────────────────────────┤
│         Integration Layer           │
│      (BankOne API, External)        │
└─────────────────────────────────────┘
```

### 2. Domain-Driven Design (DDD)
The system is organized into distinct domains:
- **Account Domain**: User and company management
- **Transfer Domain**: Payment processing and signatory workflows
- **Admin Domain**: System administration and RBAC
- **Bill Payment Domain**: Utility and service payments

### 3. Repository Pattern
Each domain implements repository patterns through Django models and managers for data access abstraction.

## System Components

### Core Applications

#### 1. Account Application
**Purpose**: Manages users, companies, and account relationships

**Key Models**:
- `User` (Django built-in)
- `Customer` - Individual users linked to companies
- `Company` - Corporate entities
- `CompanyAccount` - Bank accounts owned by companies
- `BankConstantTable` - System configuration

#### 2. Transfer Application  
**Purpose**: Handles money transfers and signatory workflows

**Key Models**:
- `SingleTransferRequest` - Individual transfer requests
- `BulkTransferRequest` - Batch transfer requests
- `AccountSignatory` - Signatory permissions per account
- `SignatoryHierarchy` - Approval workflow configuration
- `TransferApprovalWorkflow` - Approval process tracking

#### 3. SuperAdmin Application
**Purpose**: System administration and RBAC

**Key Models**:
- `AdminUser` - Administrative users
- `AdminRole` - Role definitions (checker, verifier, approver)
- `AdminPermission` - Granular permissions
- `AccountCreationRequest` - Company onboarding workflow

#### 4. Bill Payment Application
**Purpose**: Utility and service payment processing

**Key Models**:
- Bill payment beneficiaries and transactions

### Supporting Modules

#### 1. BankOne Integration (`bowenmfb.modules.bankone`)
**Purpose**: Core banking API integration

**Key Features**:
- Customer management
- Account operations
- Transfer processing
- Transaction queries
- Name enquiry services

#### 2. Utilities (`bowenmfb.modules.utils`)
**Purpose**: Common utilities and helpers

**Key Features**:
- Encryption/decryption
- Phone number formatting
- Logging utilities
- OTP generation

#### 3. Permissions (`bowenmfb.modules.permissions`)
**Purpose**: Custom permission classes

**Key Features**:
- Admin role-based permissions
- Company-specific access control
- Transfer approval permissions

## Database Architecture

### Entity Relationship Overview
```
Company ──┬── CompanyAccount ──┬── AccountSignatory
          │                    └── SignatoryHierarchy
          └── Customer ──┬── SingleTransferRequest ── TransferApprovalWorkflow
                         └── BulkTransferRequest ── TransferApprovalWorkflow

AdminUser ── AdminRole ── AdminPermission
```

### Key Relationships

1. **Company → Customer**: One-to-Many
   - Companies can have multiple employees/users

2. **Company → CompanyAccount**: One-to-Many  
   - Companies can have multiple bank accounts

3. **CompanyAccount → AccountSignatory**: One-to-Many
   - Each account can have multiple signatories

4. **CompanyAccount → SignatoryHierarchy**: One-to-One
   - Each account has one approval workflow configuration

5. **TransferRequest → TransferApprovalWorkflow**: One-to-One
   - Each transfer has one approval workflow instance

### Data Flow Patterns

#### 1. Account Creation Flow
```
User Registration → Customer Creation → Company Assignment → Account Setup → Signatory Configuration
```

#### 2. Transfer Processing Flow
```
Transfer Request → Workflow Creation → Signatory Approval → BankOne Processing → Status Update
```

## API Architecture

### RESTful Design Principles

#### 1. Resource-Based URLs
```
/accounts/                    # Account management
/transactions/               # Transfer operations
/superadmin/                # Administrative functions
/bills/                     # Bill payment services
```

#### 2. HTTP Methods Mapping
- `GET` - Retrieve resources
- `POST` - Create new resources
- `PUT/PATCH` - Update existing resources
- `DELETE` - Remove resources

#### 3. Response Format
```json
{
    "detail": "Operation description",
    "data": { /* Response payload */ },
    "pagination": { /* Pagination info */ }
}
```

### Authentication & Authorization

#### 1. JWT Authentication
```
Authorization: Bearer <jwt_token>
```

#### 2. Permission Hierarchy
```
IsAuthenticated
    ├── IsAdminUser
    │   ├── CanViewCompanies
    │   ├── CanManageCompanyUsers
    │   ├── CanCheckAccountRequests
    │   ├── CanVerifyAccountRequests
    │   └── CanApproveAccountRequests
    └── Company-based permissions
        ├── Account access
        ├── Transfer permissions
        └── Signatory rights
```

## Security Architecture

### 1. Data Protection
- **Encryption at Rest**: Sensitive data (BVN, NIN, approval pins) encrypted using Django's encryption utilities
- **Encryption in Transit**: HTTPS/TLS for all API communications
- **Field-Level Encryption**: Critical financial data encrypted before database storage

### 2. Access Control
- **Multi-Factor Authentication**: JWT tokens with expiration
- **Role-Based Access Control**: Granular permissions per admin role
- **Company Isolation**: Users can only access their company's data
- **Signatory Validation**: Transfer approval permissions validated at each level

### 3. Audit Trail
- **Complete Logging**: All financial transactions logged with timestamps
- **User Activity Tracking**: Who performed what action and when
- **Approval Chain**: Full signatory approval history maintained
- **API Request Logging**: Comprehensive request/response logging

### 4. Input Validation
- **Serializer Validation**: Django REST Framework serializers validate all inputs
- **Business Rule Validation**: Custom validation for financial business rules
- **SQL Injection Prevention**: Django ORM prevents SQL injection attacks
- **XSS Protection**: Input sanitization and output encoding

## Signatory Workflow Architecture

### Workflow State Machine
```
[Pending] → [In Progress] → [Approved] → [Executed]
    ↓           ↓              ↓
[Declined]  [Declined]    [Declined]
```

### Approval Levels
```
Level 1: Uploader    (Creates transfer request)
Level 2: Checker     (Reviews and validates)
Level 3: Verifier    (Confirms details)
Level 4: Approver    (Authorizes transfer)
Level 5: Final       (Ultimate approval)
```

### Permission Matrix
```
Role        | Upload | Check | Verify | Approve | Decline
------------|--------|-------|--------|---------|--------
Uploader    |   ✓    |   ✗   |   ✗    |    ✗    |   ✗
Checker     |   ✗    |   ✓   |   ✗    |    ✗    |   ✓
Verifier    |   ✗    |   ✗   |   ✓    |    ✗    |   ✓
Approver    |   ✗    |   ✗   |   ✗    |    ✓    |   ✓
```

## Integration Architecture

### BankOne API Integration

#### 1. Client Architecture
```python
BankOneClient
    ├── Authentication Management
    ├── Request/Response Handling
    ├── Error Management
    ├── Retry Logic
    └── Environment Configuration
```

#### 2. API Endpoints Integration
- **Customer Management**: Create, update, query customers
- **Account Operations**: Balance inquiry, transaction history
- **Transfer Services**: Intra-bank and inter-bank transfers
- **Name Enquiry**: Account validation services

#### 3. Configuration Management
```python
# Environment-based configuration
BANKONE_CONFIG = {
    'dev': {
        'base_url': 'https://staging.mybankone.com',
        'timeout': 30
    },
    'prod': {
        'base_url': 'https://mybankone.com', 
        'timeout': 30
    }
}
```

### External Service Integration

#### 1. Celery Task Queue
```python
# Asynchronous task processing
@shared_task
def create_corporate_account_with_bowen(request_id, admin_id):
    # Long-running account creation process
    pass
```

#### 2. Redis Cache
- Session management
- Task queue backend
- Temporary data storage

## Deployment Architecture

### Environment Configuration

#### 1. Development Environment
```
Django Development Server
    ├── PostgreSQL Database
    ├── Local Redis
    ├── Debug Mode Enabled
    └── BankOne Staging APIs
```

#### 2. Production Environment  
```
Load Balancer
    ├── Django Application Servers (Multiple)
    ├── PostgreSQL Database (Primary/Replica)
    ├── Redis Cluster
    ├── Celery Workers
    └── BankOne Production APIs
```

### Scalability Considerations

#### 1. Horizontal Scaling
- Multiple Django application instances
- Database read replicas
- Redis clustering
- Celery worker scaling

#### 2. Performance Optimization
- Database query optimization
- API response caching
- Asynchronous task processing
- Connection pooling

### Monitoring & Observability

#### 1. Logging Strategy
```python
# Structured logging
LOGGING = {
    'version': 1,
    'handlers': {
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'bowenmfb.log',
        }
    }
}
```

#### 2. Health Checks
- Database connectivity
- BankOne API availability
- Redis connectivity
- Celery worker status

#### 3. Metrics Collection
- API response times
- Transaction success rates
- Error rates
- System resource usage

## Data Flow Diagrams

### 1. Account Creation Flow
```
User Registration → Email Verification → Company Assignment →
Account Setup → Signatory Configuration → BankOne Account Creation →
Admin Approval → Account Activation
```

### 2. Transfer Processing Flow
```
Transfer Request Creation → Signatory Workflow Initiation →
Level-by-Level Approval → BankOne API Integration →
Transaction Execution → Status Notification
```

### 3. Bulk Transfer Processing
```
CSV Upload → File Validation → Individual Transfer Creation →
Batch Workflow Creation → Parallel Approval Processing →
Batch Execution → Consolidated Reporting
```

## Error Handling & Resilience

### 1. Exception Handling Hierarchy
```python
# Custom exception hierarchy
class BowenMFBException(Exception):
    """Base exception for all BowenMFB errors"""
    pass

class BankOneAPIException(BowenMFBException):
    """BankOne API integration errors"""
    pass

class SignatoryWorkflowException(BowenMFBException):
    """Signatory approval workflow errors"""
    pass

class InsufficientPermissionException(BowenMFBException):
    """Permission and authorization errors"""
    pass
```

### 2. Retry Mechanisms
- **Exponential Backoff**: For external API calls
- **Circuit Breaker**: Prevent cascading failures
- **Dead Letter Queue**: Handle failed background tasks
- **Graceful Degradation**: Fallback mechanisms for non-critical features

### 3. Transaction Management
```python
# Database transaction handling
from django.db import transaction

@transaction.atomic
def create_transfer_with_workflow(transfer_data, workflow_data):
    """Ensure atomicity of transfer and workflow creation"""
    transfer = SingleTransferRequest.objects.create(**transfer_data)
    workflow = TransferApprovalWorkflow.objects.create(
        single_transfer_request=transfer,
        **workflow_data
    )
    return transfer, workflow
```

## Security Implementation Details

### 1. Data Encryption
```python
# Field-level encryption for sensitive data
from django_cryptography.fields import encrypt

class Customer(BaseModel):
    bvn = encrypt(models.CharField(max_length=11))
    nin = encrypt(models.CharField(max_length=11))
    phone_number = encrypt(models.CharField(max_length=20))
```

### 2. API Security Headers
```python
# Security middleware configuration
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = ********
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
```

### 3. Input Validation
```python
# Comprehensive input validation
class TransferRequestSerializer(serializers.ModelSerializer):
    amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))]
    )

    def validate_beneficiary_account_number(self, value):
        if not value.isdigit() or len(value) != 10:
            raise serializers.ValidationError(
                "Account number must be 10 digits"
            )
        return value
```

## Testing Strategy

### 1. Test Pyramid
```
                    /\
                   /  \
                  /    \
                 / Unit \
                /  Tests \
               /_________ \
              /            \
             /  Integration \
            /      Tests     \
           /__________________\
          /                    \
         /      End-to-End      \
        /          Tests         \
       /__________________________\
```

### 2. Test Categories
- **Unit Tests**: Model validation, serializer logic, utility functions
- **Integration Tests**: API endpoints, database interactions, external services
- **End-to-End Tests**: Complete user workflows, signatory approval processes
- **Performance Tests**: Load testing, stress testing, scalability validation

### 3. Test Data Management
```python
# Factory pattern for test data
import factory

class CompanyFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Company

    name = factory.Faker('company')
    email = factory.Faker('company_email')
    rc_number = factory.Sequence(lambda n: f"RC{n:06d}")
```

## Monitoring & Observability

### 1. Application Metrics
- **Response Time**: API endpoint performance tracking
- **Error Rate**: Failed request monitoring
- **Throughput**: Requests per second measurement
- **Database Performance**: Query execution time tracking

### 2. Business Metrics
- **Transfer Success Rate**: Percentage of successful transfers
- **Approval Time**: Average time for signatory approvals
- **User Activity**: Login frequency and feature usage
- **Revenue Metrics**: Transaction volumes and fees

### 3. Alerting Strategy
```python
# Custom monitoring decorators
def monitor_api_performance(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            # Log success metrics
            return result
        except Exception as e:
            # Log error metrics and alert
            raise
        finally:
            execution_time = time.time() - start_time
            # Log performance metrics
    return wrapper
```

## Future Enhancements

### 1. Microservices Migration
- **Service Decomposition**: Split monolith into domain services
- **API Gateway**: Centralized routing and authentication
- **Service Mesh**: Inter-service communication management
- **Event-Driven Architecture**: Asynchronous service communication

### 2. Advanced Features
- **Machine Learning**: Fraud detection and risk assessment
- **Real-time Analytics**: Live transaction monitoring dashboards

### 3. Scalability Improvements
- **Auto-scaling**: Dynamic resource allocation
- **Global Distribution**: Multi-region deployment
- **Edge Computing**: Reduced latency through edge nodes
- **Serverless Functions**: Event-driven processing



## Conclusion

The BowenMFB Corporate Banking System implements a robust, scalable architecture that supports complex financial workflows while maintaining security and compliance requirements. The signatory-based approval system provides the flexibility needed for various corporate governance structures while ensuring proper audit trails and access controls.

The modular design allows for easy extension and maintenance, while the integration with BankOne APIs provides reliable core banking functionality. The system is designed to handle both current requirements and future growth in transaction volume and feature complexity.

Key architectural strengths include:
- **Separation of Concerns**: Clear domain boundaries and responsibilities
- **Scalability**: Horizontal scaling capabilities with load balancing
- **Security**: Multi-layered security with encryption and access controls
- **Reliability**: Comprehensive error handling and retry mechanisms
- **Maintainability**: Clean code structure with comprehensive testing
- **Observability**: Detailed monitoring and alerting capabilities

This architecture provides a solid foundation for a production-ready corporate banking solution that can evolve with changing business requirements and scale to meet growing demand.
