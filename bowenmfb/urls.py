from django.contrib import admin
from django.http import JsonResponse
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from account.views import BankConstantUpdateAPIView, FetchAccountOfficersAPIView, UpdateBankListAPIView


def homepage(request):
    return JsonResponse({"message": "Welcome to BowenMFB API Backend (Corporate)"})

# CRON
def process_scheduled_transfers(request):
    from transfer.cron import process_scheduled_transfers
    process_scheduled_transfers()
    return JsonResponse({"message": "Scheduled transfers processed"})


urlpatterns = [
    path('backendadmin/', admin.site.urls),
    path("", homepage),
    path("update-bank-info", BankConstantUpdateAPIView.as_view()),
    path('fetch-account-officers/', FetchAccountOfficersAPIView.as_view(), name='fetch-account-officers'),
    path('update-bank-list', UpdateBankListAPIView.as_view(), name='update-bank-list'),
    path('transactions/', include("transfer.urls")),
    path('accounts/', include("account.urls")),
    path('superadmin/', include("superadmin.urls")),
    path('bills/', include("billpayment.urls")),

    # CRON
    path('cron/process-scheduled-transfers', process_scheduled_transfers, name='process-scheduled-transfers'),

    # SWAGGER UI
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),

]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

