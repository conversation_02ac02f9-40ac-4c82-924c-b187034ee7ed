"""
Basic tests for the BankOne API client.

Run with: python manage.py shell < bowenmfb/modules/test_bankone.py
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bowenmfb.settings.dev')
django.setup()

from bowenmfb.modules.bankone import BankOneClient, get_bankone_client
from bowenmfb.modules.exceptions import (
    BankOneAPIError, 
    BankOneAuthenticationError,
    BankOneNetworkError
)
from account.models import BankConstantTable


def test_client_initialization():
    """Test that the client can be initialized."""
    print("Testing client initialization...")
    
    try:
        client = BankOneClient()
        print("✓ BankOneClient initialized successfully")
        
        # Test convenience function
        client2 = get_bankone_client()
        print("✓ get_bankone_client() works")
        
        return True
    except Exception as e:
        print(f"✗ Client initialization failed: {e}")
        return False


def test_amount_multiplication():
    """Test amount multiplication functionality."""
    print("\nTesting amount multiplication...")
    
    try:
        client = BankOneClient()
        
        # Test various amounts
        test_cases = [
            (100.0, 10000),
            (50.50, 5050),
            (1.0, 100),
            (0.01, 1)
        ]
        
        for amount, expected in test_cases:
            result = client._multiply_amount(amount)
            if result == expected:
                print(f"✓ {amount} -> {result} (expected {expected})")
            else:
                print(f"✗ {amount} -> {result} (expected {expected})")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Amount multiplication test failed: {e}")
        return False


def test_bank_config_access():
    """Test bank configuration access."""
    print("\nTesting bank configuration access...")
    
    try:
        client = BankOneClient()
        
        # This will fail if no bank config exists, which is expected
        try:
            config = client.bank_config
            print(f"✓ Bank config loaded: {config.name if hasattr(config, 'name') else 'Config object'}")
            
            # Test auth token access
            try:
                token = client.auth_token
                print("✓ Auth token accessible")
            except BankOneAuthenticationError:
                print("! Auth token not configured (expected in test environment)")
            
            # Test institution code access
            try:
                code = client.institution_code
                print("✓ Institution code accessible")
            except BankOneAPIError:
                print("! Institution code not configured (expected in test environment)")
                
        except BankOneAPIError as e:
            print(f"! Bank configuration not found (expected in test environment): {e}")
        
        return True
    except Exception as e:
        print(f"✗ Bank config test failed: {e}")
        return False


def test_url_construction():
    """Test URL construction for various endpoints."""
    print("\nTesting URL construction...")
    
    try:
        client = BankOneClient()
        
        # Test that methods exist and can be called (will fail at auth, but that's OK)
        methods_to_test = [
            'create_customer',
            'create_organization_customer',
            'create_account_quick',
            'get_account_by_number',
            'get_accounts_by_customer_id',
            'generate_account_statement',
            'freeze_account',
            'unfreeze_account',
            'get_bvn_details',
            'name_enquiry',
            'inter_bank_transfer',
            'local_funds_transfer',
            'inter_bank_transaction_status',
            'local_transaction_status',
            'transaction_reversal',
            'send_bulk_sms',
            'send_email'
        ]
        
        for method_name in methods_to_test:
            if hasattr(client, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ Method {method_name} missing")
                return False
        
        return True
    except Exception as e:
        print(f"✗ URL construction test failed: {e}")
        return False


def test_pdf_utilities():
    """Test PDF utility functions."""
    print("\nTesting PDF utilities...")
    
    try:
        client = BankOneClient()
        
        # Test with sample base64 data
        sample_base64 = "JVBERi0xLjQKJeLjz9MKNCAwIG9iago8PC9MZW5ndGggMTQ1Ny9GaWx0ZXIvRmxhdGVEZWNvZGU+PnN0cmVhbQ=="
        
        # Test decode
        pdf_bytes = client.decode_pdf_statement(sample_base64)
        print(f"✓ PDF decode successful, got {len(pdf_bytes)} bytes")
        
        # Test save (to temporary file)
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            client.save_pdf_statement(sample_base64, tmp.name)
            print(f"✓ PDF save successful to {tmp.name}")
            
            # Clean up
            os.unlink(tmp.name)
        
        return True
    except Exception as e:
        print(f"✗ PDF utilities test failed: {e}")
        return False


def run_all_tests():
    """Run all tests."""
    print("Running BankOne API Client Tests")
    print("=" * 40)
    
    tests = [
        test_client_initialization,
        test_amount_multiplication,
        test_bank_config_access,
        test_url_construction,
        test_pdf_utilities
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed - this may be expected in a test environment")


if __name__ == "__main__":
    run_all_tests()
