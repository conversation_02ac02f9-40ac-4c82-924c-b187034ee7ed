import base64
import calendar
import datetime
import decimal
import json
import logging
import uuid

import requests
from django.conf import settings

from dateutil.relativedelta import relativedelta
from cryptography.fernet import Fernet
from django.db.models.aggregates import Sum

from account.models import Customer
from billpayment.models import BillPayment
from bowenmfb.modules.exceptions import InvalidRequestException
from transfer.models import SingleTransfer, BulkTransfer


def log_request(*args):
    for arg in args:
        logging.info(arg)


def format_phone_number(phone_number):
    phone_number = f"0{phone_number[-10:]}"
    return phone_number


def encrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    secure = fernet.encrypt(f"{text}".encode())
    return secure.decode()


def decrypt_text(text: str):
    key = base64.urlsafe_b64encode(settings.SECRET_KEY.encode()[:32])
    fernet = Fernet(key)
    decrypt = fernet.decrypt(text.encode())
    return decrypt.decode()


def generate_transaction_reference():
    # Provider's length is 12 characters
    from transfer.models import SingleTransfer, BulkTransfer
    from datetime import datetime

    now = datetime.now()
    year = str(now.year)[2:]
    month = str(now.month)
    if len(month) == 1:
        month = f"0{month}"
    unique_identifier = str(uuid.uuid4().int)[:7]

    while True:
        ref_code = f"B{year}{month}{unique_identifier}"
        print(ref_code)
        if not (SingleTransfer.objects.filter(reference=ref_code).exists() or
                BulkTransfer.objects.filter(reference=ref_code).exists()):
            return ref_code


def generate_random_otp(count: int):
    return str(uuid.uuid4().int)[:count]


def get_previous_date(date, delta):
    previous_date = date - datetime.timedelta(days=delta)
    return previous_date


def get_next_date(date, delta):
    next_date = date + datetime.timedelta(days=delta)
    return next_date


def get_next_weekday(date, weekday):
    days_ahead = weekday - date.weekday()
    if days_ahead <= 0:
        days_ahead += 7
    return date + datetime.timedelta(days_ahead)


def get_week_start_and_end_datetime(date_time):
    week_start = date_time - datetime.timedelta(days=date_time.weekday())
    week_end = week_start + datetime.timedelta(days=6)
    week_start = datetime.datetime.combine(week_start.date(), datetime.time.min)
    week_end = datetime.datetime.combine(week_end.date(), datetime.time.max)
    return week_start, week_end


def get_month_start_and_end_datetime(date_time):
    month_start = date_time.replace(day=1)
    month_end = month_start.replace(day=calendar.monthrange(month_start.year, month_start.month)[1])
    month_start = datetime.datetime.combine(month_start.date(), datetime.time.min)
    month_end = datetime.datetime.combine(month_end.date(), datetime.time.max)
    return month_start, month_end


def get_year_start_and_end_datetime(date_time):
    year_start = date_time.replace(day=1, month=1, year=date_time.year)
    year_end = date_time.replace(day=31, month=12, year=date_time.year)
    year_start = datetime.datetime.combine(year_start.date(), datetime.time.min)
    year_end = datetime.datetime.combine(year_end.date(), datetime.time.max)
    return year_start, year_end


def get_previous_month_date(date, delta):
    return date - relativedelta(months=delta)


def mask_number(number):
    first_covered = number[0:3]
    second_covered = number[-3:]
    total_covered = first_covered + "******" + second_covered
    return total_covered


def get_next_minute(date, delta):
    next_minute = date + relativedelta(minutes=delta)
    return next_minute


def get_previous_minute(date, delta):
    previous_minute = date - relativedelta(minutes=delta)
    return previous_minute


def get_previous_seconds(date, delta):
    previous_seconds = date - relativedelta(seconds=delta)
    return previous_seconds


def get_previous_hour(date, delta):
    previous_hour = date - relativedelta(hours=delta)
    return previous_hour


def get_account_balance(account_number):
    from bowenmfb.modules.bankone import BankOneClient
    from account.models import CompanyAccount

    client = BankOneClient()
    try:
        account_id = CompanyAccount.objects.get(account_number=account_number).id
    except CompanyAccount.DoesNotExist:
        account_id = None

    available = 0.0
    ledger = 0.0
    withdraw_able = 0.0
    response = client.get_account_by_number(account_number)
    if "AvailableBalance" in response:
        available = decimal.Decimal(str(response["AvailableBalance"]).replace(",", ""))
    if "LedgerBalance" in response:
        ledger = decimal.Decimal(str(response["LedgerBalance"]).replace(",", ""))
    if "WithdrawableBalance" in response:
        withdraw_able = decimal.Decimal(str(response["WithdrawableBalance"]).replace(",", ""))

    balance_data = {
        "account_id": account_id,
        "account_number": mask_number(account_number),
        "balances": {
            "available_balance": available,
            "ledger_balance": ledger,
            "withdrawable_balance": withdraw_able
        }
    }

    return balance_data


def validate_bvn_with_phone_number(bvn_number, phone_number):
    from bowenmfb.modules.bankone import BankOneClient
    client = BankOneClient()

    response = client.get_bvn_details(bvn=bvn_number)

    if ("RequestStatus" in response and response["RequestStatus"] is True) and ("isBvnValid" in response and response["isBvnValid"] is True):
        bvn_phone_number = response["bvnDetails"]["phoneNumber"]
        if settings.DEBUG is True:
            bvn_phone_number = phone_number
        if str(phone_number).strip() == str(bvn_phone_number).strip():
            return True

    return False


def send_tmsaas_sms(content, receiver):
    baseUrl = settings.TMSAAS_BASE_URL
    header = {
        "client-id": settings.TMSAAS_CLIENT_ID,
        "Content-Type": "application/json"
    }
    url = f"{baseUrl}/sms"
    payload = json.dumps({"message": content, "provider": settings.TMSAAS_SENDER_ID, "recipients": [receiver]})
    log_request("POST", f"url: {url}", f"header: {header}", f"payload: {payload}")
    response = requests.request("POST", url=url, headers=header, data=payload)
    log_request(f"response: {response.text}")
    return response.json()


def send_tmsaas_email(content, receiver, subject):
    from account.models import BankConstantTable
    constant = BankConstantTable.objects.first()
    baseUrl = settings.TMSAAS_BASE_URL
    header = {
        "client-id": settings.TMSAAS_CLIENT_ID,
        "Content-Type": "application/json"
    }
    url = f"{baseUrl}/email"
    payload = json.dumps(
        {
            "from": str(constant.support_email) or "<EMAIL>",
            "type": "custom",
            "html": content,
            "subject": subject,
            "recipients": [
                receiver
            ]
        }
    )
    log_request("POST", f"url: {url}", f"header: {header}", f"payload: {payload}")
    response = requests.request("POST", url=url, headers=header, data=payload)
    log_request(f"response: {response.text}")
    return response.json()


def send_notification_async(
    notification_type: str,
    recipient: str,
    message: str,
    subject: str = None,
    account_number: str = None,
    reference_no: str = None,
):
    """
    Helper function to send notifications asynchronously using Celery.

    Args:
        notification_type: 'sms' or 'email'
        recipient: Phone number for SMS or email address for email
        message: Message content
        subject: Email subject (required for email)
        account_number: Account number for SMS (optional, will use default if not provided)
        reference_no: Reference number for SMS (optional, will generate if not provided)
        institution_code: Institution code for email (has default)
        mfb_code: MFB code for email (has default)
        email_from: From email address (has default)

    Returns:
        AsyncResult: Celery task result
    """
    from superadmin.tasks import send_notification_task
    from account.models import BankConstantTable

    # Generate reference number if not provided for SMS
    if notification_type == 'sms' and not reference_no:
        reference_no = f"BowenSMS{generate_random_otp(6)}"

    # Use default account number if not provided for SMS
    if notification_type == 'sms' and not account_number:
        # You might want to get this from settings or database
        account_number = getattr(settings, 'DEFAULT_SMS_ACCOUNT_NUMBER', '**********')

    bank_config = BankConstantTable.objects.first()
    institution_code = bank_config.institution_code
    mfb_code = bank_config.mfb_code
    email_from = bank_config.support_email

    return send_notification_task.delay(
        notification_type=notification_type,
        recipient=recipient,
        message=message,
        subject=subject,
        account_number=account_number,
        reference_no=reference_no,
        institution_code=institution_code,
        mfb_code=mfb_code,
        email_from=email_from
    )


def check_transaction_limit(account_id, amount):
    from account.models import CompanyAccount
    from transfer.models import SingleTransfer, BulkTransfer
    from billpayment.models import BillPayment

    account = CompanyAccount.objects.get(id=account_id)

    daily_limit = account.daily_transfer_limit
    monthly_limit = account.monthly_transfer_limit

    today = datetime.date.today()
    current_month = today.month

    # Check daily limit
    daily_single_transfers_amount = SingleTransfer.objects.filter(from_account=account,
        company=account.company, created_on__date=today, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    daily_bulk_transfers_amount = BulkTransfer.objects.filter(from_account=account,
        company=account.company, created_on__date=today, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    daily_bill_payments_amount = BillPayment.objects.filter(from_account=account,
        company=account.company, created_on__date=today, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    if daily_single_transfers_amount + daily_bulk_transfers_amount + daily_bill_payments_amount + amount > daily_limit:
        raise InvalidRequestException({"detail": "Daily transfer limit exceeded"})

    # Check monthly limit
    monthly_single_transfers_amount = SingleTransfer.objects.filter(from_account=account,
        company=account.company, created_on__month=current_month, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    monthly_bulk_transfers_amount = BulkTransfer.objects.filter(from_account=account,
        company=account.company, created_on__month=current_month, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    monthly_bill_payments_amount = BillPayment.objects.filter(from_account=account,
        company=account.company, created_on__month=current_month, status="success").aggregate(Sum('amount'))['amount__sum'] or 0.0

    if monthly_single_transfers_amount + monthly_bulk_transfers_amount + monthly_bill_payments_amount + amount > monthly_limit:
        raise InvalidRequestException({"detail": "Monthly transfer limit exceeded"})

    return True


def create_in_app_notification(company, title, message, notification_type='general', related_object_id=None, related_object_type=None):
    from account.models import InAppNotification

    """
    Helper function to create in-app notifications.

    Args:
        company: Company instance
        title: Notification title
        message: Notification message
        notification_type: Type of notification
        related_object_id: Optional ID of related object
        related_object_type: Optional type of related object

    Returns:
        InAppNotification instance
    """

    logger = logging.getLogger(__name__)

    try:
        notification = InAppNotification.objects.create(
            company=company,
            title=title,
            message=message,
            notification_type=notification_type,
            related_object_id=related_object_id,
            related_object_type=related_object_type
        )
        log_request(f"Created in-app notification for {company.name}: {title}")
        return notification
    except Exception as e:
        logger.error(f"Error creating in-app notification: {str(e)}")
        log_request(f"Error creating in-app notification: {str(e)}")
        return None


def get_superadmin_dashboard_data(request):
    ...



