"""
Examples for using the BankOne messaging endpoints and notification tasks.

This module demonstrates how to:
1. Use BankOne API directly for SMS and email
2. Use the Celery task for asynchronous notifications
3. Use the helper function for easy notification sending
"""

from bowenmfb.modules.bankone import BankOneClient, BankOneAPIError
from bowenmfb.modules.utils import send_notification_async
from superadmin.tasks import send_notification_task


def example_bankone_sms():
    """Example: Send SMS using BankOne API directly."""
    client = BankOneClient()
    
    try:
        sms_messages = [
            {
                "AccountNumber": "**********",
                "To": "***********",
                "Body": "Your account balance is N50,000. Thank you for banking with us.",
                "ReferenceNo": "SMS001"
            },
            {
                "AccountNumber": "**********",
                "To": "***********",
                "Body": "Your transfer of N10,000 was successful. Ref: TXN123456",
                "ReferenceNo": "SMS002"
            }
        ]
        
        response = client.send_bulk_sms(sms_messages)
        print(f"SMS sent successfully: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to send SMS: {e}")
        return None


def example_bankone_email():
    """Example: Send email using BankOne API directly."""
    client = BankOneClient()
    
    try:
        email_messages = [
            {
                "InstitutionCode": "223333",
                "MfbCode": "123422",
                "emailFrom": "<EMAIL>",
                "emailTo": "<EMAIL>",
                "Subject": "Account Statement",
                "Message": "Dear Customer, please find your monthly account statement attached. Thank you for banking with us."
            },
            {
                "InstitutionCode": "223333",
                "MfbCode": "123422",
                "emailFrom": "<EMAIL>",
                "emailTo": "<EMAIL>",
                "Subject": "Transfer Notification",
                "Message": "Your transfer request has been processed successfully. Reference: TXN789012"
            }
        ]
        
        response = client.send_email(email_messages)
        print(f"Email sent successfully: {response}")
        return response
        
    except BankOneAPIError as e:
        print(f"Failed to send email: {e}")
        return None


def example_async_sms_notification():
    """Example: Send SMS using Celery task (asynchronous)."""
    try:
        # This will use tmsaas if DEBUG=True, otherwise BankOne API
        task_result = send_notification_task.delay(
            notification_type='sms',
            recipient='***********',
            message='Your OTP is 123456. Valid for 5 minutes.',
            account_number='**********',
            reference_no='OTP001'
        )
        
        print(f"SMS task queued with ID: {task_result.id}")
        
        # You can check the result later
        # result = task_result.get(timeout=30)
        # print(f"SMS result: {result}")
        
        return task_result
        
    except Exception as e:
        print(f"Failed to queue SMS task: {e}")
        return None


def example_async_email_notification():
    """Example: Send email using Celery task (asynchronous)."""
    try:
        # This will use tmsaas if DEBUG=True, otherwise BankOne API
        task_result = send_notification_task.delay(
            notification_type='email',
            recipient='<EMAIL>',
            message='Welcome to Bowen MFB! Your account has been successfully created.',
            subject='Welcome to Bowen MFB',
            institution_code='223333',
            mfb_code='123422',
            email_from='<EMAIL>'
        )
        
        print(f"Email task queued with ID: {task_result.id}")
        
        # You can check the result later
        # result = task_result.get(timeout=30)
        # print(f"Email result: {result}")
        
        return task_result
        
    except Exception as e:
        print(f"Failed to queue email task: {e}")
        return None


def example_helper_function_sms():
    """Example: Send SMS using the helper function."""
    try:
        task_result = send_notification_async(
            notification_type='sms',
            recipient='***********',
            message='Your transaction was successful. Amount: N5,000'
            # account_number and reference_no will be auto-generated if not provided
        )
        
        print(f"SMS notification queued with ID: {task_result.id}")
        return task_result
        
    except Exception as e:
        print(f"Failed to send SMS notification: {e}")
        return None


def example_helper_function_email():
    """Example: Send email using the helper function."""
    try:
        task_result = send_notification_async(
            notification_type='email',
            recipient='<EMAIL>',
            message='Your monthly statement is ready for download.',
            subject='Monthly Statement Available'
            # institution_code, mfb_code, and email_from will use defaults
        )
        
        print(f"Email notification queued with ID: {task_result.id}")
        return task_result
        
    except Exception as e:
        print(f"Failed to send email notification: {e}")
        return None


def example_bulk_notifications():
    """Example: Send multiple notifications efficiently."""
    recipients = [
        {'phone': '***********', 'email': '<EMAIL>', 'name': 'John Doe'},
        {'phone': '***********', 'email': '<EMAIL>', 'name': 'Jane Smith'},
        {'phone': '***********', 'email': '<EMAIL>', 'name': 'Bob Johnson'},
    ]
    
    sms_tasks = []
    email_tasks = []
    
    for recipient in recipients:
        # Send SMS
        sms_task = send_notification_async(
            notification_type='sms',
            recipient=recipient['phone'],
            message=f"Hello {recipient['name']}, your account balance is available online."
        )
        sms_tasks.append(sms_task)
        
        # Send Email
        email_task = send_notification_async(
            notification_type='email',
            recipient=recipient['email'],
            message=f"Dear {recipient['name']}, your monthly statement is now available.",
            subject='Monthly Statement Ready'
        )
        email_tasks.append(email_task)
    
    print(f"Queued {len(sms_tasks)} SMS tasks and {len(email_tasks)} email tasks")
    return {'sms_tasks': sms_tasks, 'email_tasks': email_tasks}


if __name__ == "__main__":
    print("=== BankOne Messaging Examples ===")
    
    print("\n1. Direct BankOne SMS:")
    example_bankone_sms()
    
    print("\n2. Direct BankOne Email:")
    example_bankone_email()
    
    print("\n3. Async SMS Notification:")
    example_async_sms_notification()
    
    print("\n4. Async Email Notification:")
    example_async_email_notification()
    
    print("\n5. Helper Function SMS:")
    example_helper_function_sms()
    
    print("\n6. Helper Function Email:")
    example_helper_function_email()
    
    print("\n7. Bulk Notifications:")
    example_bulk_notifications()
