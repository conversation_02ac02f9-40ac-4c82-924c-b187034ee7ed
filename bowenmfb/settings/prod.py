from datetime import timedelta
from .base import *

SECRET_KEY = env('SECRET_KEY')

# BankOne API Configuration (fallback if not configured in database)
BANKONE_BASE_URL = env('BANKONE_BASE_URL', default='https://mybankone.com')

DEBUG = False

ALLOWED_HOSTS = ["*"]

CORS_ALLOWED_ORIGINS = [
    "https://api.bowenmfb.com",
    "https://bowenmfb.com",
]

CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS

# Database
DATABASES = {
    'default': {
        'ENGINE': env('DATABASE_ENGINE', None),
        'NAME': env('DATABASE_NAME', None),
        'USER': env('DATABASE_USER', None),
        'PASSWORD': env('DATABASE_PASSWORD', None),
        'HOST': env('DATABASE_HOST', None),
        'PORT': env('DATABASE_PORT', None),
    },
}
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# Simple JWT
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'UPDATE_LAST_LOGIN': True,
    'AUTH_HEADER_TYPES': ('Bearer', 'Token',),
}

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{asctime}] {levelname} {module} {thread:d} - {message}',
            'style': '{',
            'datefmt': '%d-%m-%Y %H:%M:%S'
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'bowenmfb.log'),
            'when': 'midnight',
            'interval': 1,  # daily
            'backupCount': 60,  # Keep logs for 60 days
            'formatter': 'verbose',
        }
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.server': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# CELERY
CELERY_BROKER_URL = env('CELERY_BROKER_URL', None)
CELERY_RESULT_BACKEND = env('CELERY_RESULT_BACKEND', None)

# TMSAAS
TMSAAS_BASE_URL = env('TMSAAS_BASE_URL', default='https://tm30.net')
TMSAAS_CLIENT_ID = env('TMSAAS_CLIENT_ID', default='client_id')
TMSAAS_CLIENT_SECRET = env('TMSAAS_CLIENT_SECRET', default='client_secret')
TMSAAS_SENDER_ID = env('TMSAAS_SENDER_ID', default='sender_id')

DEFAULT_SMS_ACCOUNT_NUMBER = env('DEFAULT_SMS_ACCOUNT_NUMBER', default='**********')



