from datetime import timed<PERSON>ta
from .base import *


SECRET_KEY = env('SECRET_KEY')

BASE_URL = env('BASE_URL')

# BankOne API Configuration (fallback if not configured in database)
BANKONE_BASE_URL = env('BANKONE_BASE_URL', default='https://staging.mybankone.com')

DEBUG = True

ALLOWED_HOSTS = ["*"]

CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8080",
    "http://localhost:8000",
    "http://localhost:4200",
    "http://localhost:80",
    "http://localhost:3000",
    "http://localhost:5000",
    "https://bowenmfbapi.tm-dev.xyz",
    "http://localhost",
    "http://127.0.0.1"
]

CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS


# Database
DATABASES = {
    'default': {
        'ENGINE': env('DATABASE_ENGINE', None),
        'NAME': env('DATABASE_NAME', None),
        'USER': env('DATABASE_USER', None),
        'PASSWORD': env('DATABASE_PASSWORD', None),
        'HOST': env('DATABASE_HOST', None),
        'PORT': env('DATABASE_PORT', None),
    }
}
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases


# Simple JWT
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=60),
    'UPDATE_LAST_LOGIN': True,
    'AUTH_HEADER_TYPES': ('Bearer', 'Token',),
}

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{asctime}] {levelname} {module} {thread:d} - {message}',
            'style': '{',
            'datefmt': '%d-%m-%Y %H:%M:%S'
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOG_DIR, 'bowenmfb.log'),
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.server': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.request': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# CELERY
CELERY_BROKER_URL = env('CELERY_BROKER_URL', None)
CELERY_RESULT_BACKEND = env('CELERY_RESULT_BACKEND', None)

# TMSAAS
TMSAAS_BASE_URL = env('TMSAAS_BASE_URL', default='https://tm30.net')
TMSAAS_CLIENT_ID = env('TMSAAS_CLIENT_ID', default='client_id')
TMSAAS_CLIENT_SECRET = env('TMSAAS_CLIENT_SECRET', default='client_secret')
TMSAAS_SENDER_ID = env('TMSAAS_SENDER_ID', default='sender_id')

DEFAULT_SMS_ACCOUNT_NUMBER = env('DEFAULT_SMS_ACCOUNT_NUMBER', default='**********')


