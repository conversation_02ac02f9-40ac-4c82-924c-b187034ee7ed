"""
ASGI config for bowenmfb project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os
from decouple import config
from django.core.asgi import get_asgi_application

if config('env', '') == 'prod' or os.getenv('env', 'dev') == 'prod':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bowenmfb.settings.prod')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'bowenmfb.settings.dev')

application = get_asgi_application()
