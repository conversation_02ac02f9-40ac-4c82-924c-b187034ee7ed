from django.core.exceptions import ValidationError
from django.db import models

from account.models import BaseModel, Company, CompanyAccount, Customer
from bowenmfb.modules.choices import TRANSFER_BENEFICIARY_TYPE_CHOICES, SCHEDULE_TYPE, \
    DAYS_OF_THE_MONTH_CHOICES, DAY_OF_THE_WEEK_CHOICES, TRANSFER_SCHEDULE_STATUS, STATUS_CHOICES, SIGNATORY_ROLE_CHOICES, HIERARCHY_LEVEL_CHOICES, \
    WORKFLOW_STATUS_CHOICES, TRANSFER_SCHEDULE_CATEGORY_CHOICES


class BankList(BaseModel):
    name = models.Char<PERSON>ield(max_length=300)
    code = models.Char<PERSON>ield(max_length=200)

    def __str__(self):
        return self.name


class TransferBeneficiary(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    beneficiary_type = models.Cha<PERSON><PERSON><PERSON>(max_length=200, choices=TRANSFER_BENEFICIARY_TYPE_CHOICES, default='intra')
    name = models.CharField(max_length=200)
    bank_name = models.CharField(max_length=200)
    bank_code = models.CharField(max_length=200, blank=True, null=True)
    account_number = models.CharField(max_length=200, blank=True, null=True)

    class Meta:
        verbose_name = 'Transfer Beneficiary'
        verbose_name_plural = 'Transfer Beneficiaries'

    def __str__(self):
        return f"{self.company.name}: {self.name}"


class TransferScheduler(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, default=None)
    schedule_type = models.CharField(max_length=100, choices=SCHEDULE_TYPE, default="once")
    day_of_the_month = models.CharField(max_length=200, choices=DAYS_OF_THE_MONTH_CHOICES, blank=True, null=True)
    day_of_the_week = models.CharField(max_length=100, choices=DAY_OF_THE_WEEK_CHOICES, blank=True, null=True)
    status = models.CharField(max_length=50, choices=TRANSFER_SCHEDULE_STATUS, default="inactive")
    category = models.CharField(max_length=100, choices=TRANSFER_SCHEDULE_CATEGORY_CHOICES, default="beneficiary_payment")
    completed = models.BooleanField(default=False)
    start_date = models.DateTimeField(null=True)
    end_date = models.DateTimeField(null=True)
    last_job_date = models.DateTimeField(null=True, blank=True)
    next_job_date = models.DateTimeField(null=True, blank=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.schedule_type}: {self.completed}"


class SingleTransferRequest(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True)
    amount = models.FloatField(default=1.0)
    description = models.CharField(max_length=60)
    beneficiary_type = models.CharField(max_length=50, choices=TRANSFER_BENEFICIARY_TYPE_CHOICES, default="intra")
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)
    beneficiary_account_number = models.CharField(max_length=20)
    beneficiary_name = models.CharField(max_length=100)
    beneficiary_bank_code = models.CharField(max_length=20, blank=True, null=True)
    nip_session_id = models.CharField(max_length=200, blank=True, null=True)
    bank_name = models.CharField(max_length=100, blank=True, null=True)
    beneficiary_acct_type = models.CharField(max_length=20, blank=True, null=True)
    scheduled = models.BooleanField(default=False)
    scheduler = models.ForeignKey(TransferScheduler, on_delete=models.SET_NULL, null=True, blank=True)
    decline_reason = models.CharField(max_length=250, blank=True, null=True)
    provider_response = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_created_by", null=True, on_delete=models.SET_NULL)
    is_checked = models.BooleanField(default=False)
    checked_at = models.DateTimeField(blank=True, null=True)
    checked_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_checked_by", null=True, on_delete=models.SET_NULL)
    is_verified = models.BooleanField(default=False)
    verified_at = models.DateTimeField(blank=True, null=True)
    verified_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_verified_by", null=True, on_delete=models.SET_NULL)
    is_approved = models.BooleanField(default=False)
    approved_at = models.DateTimeField(blank=True, null=True)
    approved_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_approved_by", null=True, on_delete=models.SET_NULL)
    is_declined = models.BooleanField(default=False)
    declined_at = models.DateTimeField(blank=True, null=True)
    declined_by = models.ForeignKey(Customer, blank=True, related_name="single_transfer_declined_by", null=True, on_delete=models.SET_NULL)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)


class SingleTransfer(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, blank=True, null=True)
    transfer_request = models.ForeignKey(SingleTransferRequest, on_delete=models.SET_NULL, blank=True, null=True)
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)
    transfer_type = models.CharField(max_length=100, choices=TRANSFER_BENEFICIARY_TYPE_CHOICES, default='intra')
    beneficiary_name = models.CharField(max_length=200)
    beneficiary_bank_name = models.CharField(max_length=200)
    beneficiary_acct_number = models.CharField(max_length=20)
    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default='pending')
    amount = models.FloatField(default=1.0)
    fee = models.FloatField(default=0.0)
    narration = models.TextField(blank=True, null=True)
    reference = models.CharField(max_length=12, blank=True, null=True)
    provider_reference = models.CharField(max_length=200, blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.beneficiary_name} - {self.beneficiary_bank_name}"


class BulkTransferRequest(BaseModel):
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True)
    description = models.CharField(max_length=200)
    total_amount = models.FloatField(default=0.0)
    scheduled = models.BooleanField(default=False)
    scheduler = models.ForeignKey(TransferScheduler, on_delete=models.SET_NULL, null=True, blank=True)
    request_payload = models.TextField(blank=True, null=True)
    is_checked = models.BooleanField(default=False)
    checked_by = models.ForeignKey(Customer, blank=True, related_name="bulk_transfer_checked_by", null=True, on_delete=models.SET_NULL)
    checked_at = models.DateTimeField(blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(Customer, blank=True, related_name="bulk_transfer_verified_by", null=True, on_delete=models.SET_NULL)
    verified_at = models.DateTimeField(blank=True, null=True)
    is_approved = models.BooleanField(default=False)
    approved_by = models.ForeignKey(Customer, blank=True, related_name="bulk_transfer_approved_by", null=True, on_delete=models.SET_NULL)
    approved_at = models.DateTimeField(blank=True, null=True)
    is_declined = models.BooleanField(default=False)
    declined_by = models.ForeignKey(Customer, blank=True, related_name="bulk_transfer_declined_by", null=True, on_delete=models.SET_NULL)
    declined_at = models.DateTimeField(blank=True, null=True)

    decline_reason = models.CharField(max_length=250, blank=True, null=True)
    provider_response = models.TextField(blank=True, null=True)

    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Bulk Transfer - {self.company.name if self.company else 'No Company'} (amount: {self.total_amount})"


class BulkTransfer(BaseModel):
    bulk_request = models.ForeignKey(BulkTransferRequest, on_delete=models.CASCADE, related_name='transfers')
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, blank=True, null=True)
    from_account = models.ForeignKey(CompanyAccount, on_delete=models.SET_NULL, blank=True, null=True)
    transfer_type = models.CharField(max_length=100, choices=TRANSFER_BENEFICIARY_TYPE_CHOICES, default='intra')
    beneficiary_name = models.CharField(max_length=200)
    beneficiary_bank_name = models.CharField(max_length=200)
    beneficiary_acct_number = models.CharField(max_length=20)
    beneficiary_bank_code = models.CharField(max_length=50, blank=True, null=True)
    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default='pending')
    amount = models.FloatField(default=1.0)
    fee = models.FloatField(default=0.0)
    nip_session_id = models.CharField(max_length=200, blank=True, null=True)
    narration = models.TextField(blank=True, null=True)
    reference = models.CharField(max_length=12, blank=True, null=True)
    provider_reference = models.CharField(max_length=200, blank=True, null=True)
    provider_response = models.TextField(blank=True, null=True)
    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Bulk: {self.beneficiary_name} - {self.beneficiary_bank_name}"


class AccountSignatory(BaseModel):
    """
    Model to define signatories for company accounts.
    Links customers to company accounts with their hierarchy level.
    """
    company_account = models.ForeignKey(CompanyAccount, on_delete=models.CASCADE, related_name='signatories')
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='signatory_accounts')
    role = models.CharField(max_length=20, choices=SIGNATORY_ROLE_CHOICES, default="uploader")
    hierarchy_level = models.IntegerField(choices=HIERARCHY_LEVEL_CHOICES, default=1)
    is_active = models.BooleanField(default=True)
    can_upload = models.BooleanField(default=True)
    can_check = models.BooleanField(default=False)
    can_verify = models.BooleanField(default=False)
    can_approve = models.BooleanField(default=False)

    class Meta:
        unique_together = ['company_account', 'customer']
        verbose_name = 'Account Signatory'
        verbose_name_plural = 'Account Signatories'

    def clean(self):
        # Ensure hierarchy level matches role permissions
        if self.hierarchy_level == 1 and not self.can_upload:
            self.can_upload = True
        elif self.hierarchy_level == 2 and not self.can_check:
            self.can_check = True
        elif self.hierarchy_level == 3 and not self.can_verify:
            self.can_verify = True
        elif self.hierarchy_level >= 4 and not self.can_approve:
            self.can_approve = True

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.customer.user.get_full_name()} - {self.company_account} (Level {self.hierarchy_level})"


class SignatoryHierarchy(BaseModel):
    """
    Model to define the approval workflow hierarchy for each company account.
    Determines how many levels of approval are required.
    """
    company_account = models.OneToOneField(CompanyAccount, on_delete=models.CASCADE, related_name='hierarchy')
    total_levels = models.IntegerField(default=1, help_text="Total number of approval levels (1-5)")
    requires_checker = models.BooleanField(default=False)
    requires_verifier = models.BooleanField(default=False)
    requires_approver = models.BooleanField(default=True)
    is_single_signatory = models.BooleanField(default=True, help_text="True if account is managed by single person")

    class Meta:
        verbose_name = 'Signatory Hierarchy'
        verbose_name_plural = 'Signatory Hierarchies'

    def clean(self):
        if self.total_levels < 1 or self.total_levels > 5:
            raise ValidationError("Total levels must be between 1 and 5")

        if self.is_single_signatory and self.total_levels > 1:
            raise ValidationError("Single signatory accounts can only have 1 level")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.company_account} - {self.total_levels} levels"


class TransferApprovalWorkflow(BaseModel):
    """
    Model to track the approval workflow for transfer requests.
    Records who performed each action and when.
    """
    # Link to either single or bulk transfer request
    single_transfer_request = models.OneToOneField(
        SingleTransferRequest,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='approval_workflow'
    )
    bulk_transfer_request = models.OneToOneField(
        BulkTransferRequest,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='approval_workflow'
    )

    # Workflow status
    current_level = models.IntegerField(default=1)
    status = models.CharField(max_length=20, choices=WORKFLOW_STATUS_CHOICES, default='pending')

    # Approval tracking
    uploaded_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, related_name='uploaded_transfers')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    checked_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='checked_transfers')
    checked_at = models.DateTimeField(null=True, blank=True)

    verified_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_transfers')
    verified_at = models.DateTimeField(null=True, blank=True)

    approved_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_transfers')
    approved_at = models.DateTimeField(null=True, blank=True)

    declined_by = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True, related_name='declined_transfers')
    declined_at = models.DateTimeField(null=True, blank=True)
    decline_reason = models.TextField(blank=True, null=True)
    process_response = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = 'Transfer Approval Workflow'
        verbose_name_plural = 'Transfer Approval Workflows'

    def clean(self):
        # Ensure only one transfer request is linked
        if self.single_transfer_request and self.bulk_transfer_request:
            raise ValidationError("Workflow can only be linked to either single or bulk transfer, not both")
        if not self.single_transfer_request and not self.bulk_transfer_request:
            raise ValidationError("Workflow must be linked to either single or bulk transfer request")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    @property
    def transfer_request(self):
        """Get the associated transfer request (single or bulk)"""
        return self.single_transfer_request or self.bulk_transfer_request

    @property
    def tranfer_type(self):
        """Get the type of transfer (single or bulk)"""
        return "single" if self.single_transfer_request else "bulk"

    @property
    def company_account(self):
        """Get the company account for this transfer"""
        transfer = self.transfer_request
        if self.tranfer_type == "bulk":
            return CompanyAccount.objects.filter(company=transfer.company).last()
        return transfer.from_account if transfer else None

    def can_user_perform_action(self, user, action):
        """
        Check if a user can perform a specific action on this workflow.
        Actions: 'check', 'verify', 'approve', 'decline'
        """
        try:
            customer = user.customer
            account = self.company_account
            if not account:
                return False

            signatory = AccountSignatory.objects.filter(
                company_account=account,
                customer=customer,
                is_active=True
            ).first()

            if not signatory:
                return False

            if SignatoryHierarchy.objects.filter(company_account=self.company_account).last().is_single_signatory and \
                    len(Customer.objects.filter(company=self.company_account.company)) == 1:
                return True
            elif action == 'check':
                return signatory.can_check and self.current_level == 2
            elif action == 'verify':
                return signatory.can_verify and self.current_level == 3
            elif action == 'approve':
                return signatory.can_approve and self.current_level >= 4
            elif action == 'decline':
                return True  # Any signatory can decline

            return False
        except:
            return False

    def __str__(self):
        transfer_type = "Single" if self.single_transfer_request else "Bulk"
        return f"{transfer_type} Transfer Workflow - Level {self.current_level} ({self.status})"



