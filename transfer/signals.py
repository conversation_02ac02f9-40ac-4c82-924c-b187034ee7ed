from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction

from bowenmfb.modules.utils import send_notification_async, log_request, create_in_app_notification
from .models import SignatoryHierarchy, CompanyAccount, Customer, AccountSignatory, SingleTransferRequest, BulkTransferRequest


@receiver(signal=post_save, sender=CompanyAccount)
def create_account_hierarchy(sender, instance, **kwargs):
    company = instance.company
    hierarchy, _ = SignatoryHierarchy.objects.get_or_create(company_account=instance)
    signatories = Customer.objects.filter(company=company)
    for customer in signatories:
        account_signatory, _ = AccountSignatory.objects.get_or_create(company_account=instance, customer=customer)
    total_company_signatories = len(signatories)
    hierarchy.total_levels = total_company_signatories
    if total_company_signatories > 1:
        hierarchy.is_single_signatory = False
        hierarchy.requires_approver = True
        hierarchy.requires_verifier = True
        hierarchy.requires_checker = True
    hierarchy.save()

    return True


# Transfer notification signals

@receiver(pre_save, sender=SingleTransferRequest)
def track_single_transfer_status_changes(sender, instance, **kwargs):
    """Track status changes for single transfer requests before saving."""
    if instance.pk:
        try:
            # Get the previous state from database
            old_instance = SingleTransferRequest.objects.get(pk=instance.pk)
            instance._old_is_checked = old_instance.is_checked
            instance._old_is_verified = old_instance.is_verified
            instance._old_is_approved = old_instance.is_approved
            instance._old_is_declined = old_instance.is_declined
        except SingleTransferRequest.DoesNotExist:
            instance._old_is_checked = False
            instance._old_is_verified = False
            instance._old_is_approved = False
            instance._old_is_declined = False
    else:
        instance._old_is_checked = False
        instance._old_is_verified = False
        instance._old_is_approved = False
        instance._old_is_declined = False


@receiver(post_save, sender=SingleTransferRequest)
def send_single_transfer_notifications(sender, instance, created, **kwargs):
    """Send notifications when single transfer request status changes."""
    if created:
        # Create in-app notification for new transfer request
        if instance.company:
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="New Transfer Request Created",
                message=f"A new transfer request of ₦{instance.amount:,.2f} to {instance.beneficiary_name} has been created.",
                notification_type='transfer_created',
                related_object_id=instance.id,
                related_object_type='SingleTransferRequest'
            ))
        return

    # Check if any status has changed
    status_changes = []

    if hasattr(instance, '_old_is_checked') and not instance._old_is_checked and instance.is_checked:
        status_changes.append('checked')

    if hasattr(instance, '_old_is_verified') and not instance._old_is_verified and instance.is_verified:
        status_changes.append('verified')

    if hasattr(instance, '_old_is_approved') and not instance._old_is_approved and instance.is_approved:
        status_changes.append('approved')

    if hasattr(instance, '_old_is_declined') and not instance._old_is_declined and instance.is_declined:
        status_changes.append('declined')

    # Send notifications for each status change
    for status in status_changes:
        transaction.on_commit(lambda s=status: send_single_transfer_status_notification(instance, s))


def send_single_transfer_status_notification(transfer_request, status):
    """Send notification for single transfer status change."""
    try:
        if not transfer_request.created_by:
            return

        customer = transfer_request.created_by
        company_name = transfer_request.company.name if transfer_request.company else "Your Company"

        # Prepare status-specific messages
        status_messages = {
            'checked': {
                'sms': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been checked and is now under review.",
                'email_subject': 'Transfer Request Checked',
                'email_body': f"""Dear {customer.user.get_full_name()},

Your transfer request has been checked and is now under review.

Transfer Details:
- Amount: ₦{transfer_request.amount:,.2f}
- Beneficiary: {transfer_request.beneficiary_name}
- Account Number: {transfer_request.beneficiary_account_number}
- Bank: {transfer_request.bank_name or 'N/A'}
- Description: {transfer_request.description}
- Company: {company_name}

Your request is now in the verification stage.

Best regards,
Bowen MFB Team"""
            },
            'verified': {
                'sms': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been verified and is awaiting final approval.",
                'email_subject': 'Transfer Request Verified',
                'email_body': f"""Dear {customer.user.get_full_name()},

Your transfer request has been verified and is awaiting final approval.

Transfer Details:
- Amount: ₦{transfer_request.amount:,.2f}
- Beneficiary: {transfer_request.beneficiary_name}
- Account Number: {transfer_request.beneficiary_account_number}
- Bank: {transfer_request.bank_name or 'N/A'}
- Description: {transfer_request.description}
- Company: {company_name}

Your request is now in the final approval stage.

Best regards,
Bowen MFB Team"""
            },
            'approved': {
                'sms': f"Great news! Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been approved and will be processed shortly.",
                'email_subject': 'Transfer Request Approved',
                'email_body': f"""Dear {customer.user.get_full_name()},

Great news! Your transfer request has been approved and will be processed shortly.

Transfer Details:
- Amount: ₦{transfer_request.amount:,.2f}
- Beneficiary: {transfer_request.beneficiary_name}
- Account Number: {transfer_request.beneficiary_account_number}
- Bank: {transfer_request.bank_name or 'N/A'}
- Description: {transfer_request.description}
- Company: {company_name}

The transfer will be executed and you will receive a confirmation once completed.

Best regards,
Bowen MFB Team"""
            },
            'declined': {
                'sms': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been declined. Reason: {transfer_request.decline_reason or 'Not specified'}",
                'email_subject': 'Transfer Request Declined',
                'email_body': f"""Dear {customer.user.get_full_name()},

We regret to inform you that your transfer request has been declined.

Transfer Details:
- Amount: ₦{transfer_request.amount:,.2f}
- Beneficiary: {transfer_request.beneficiary_name}
- Account Number: {transfer_request.beneficiary_account_number}
- Bank: {transfer_request.bank_name or 'N/A'}
- Description: {transfer_request.description}
- Company: {company_name}

Reason for decline: {transfer_request.decline_reason or 'Not specified'}

Please contact your account officer for more information or to submit a new request.

Best regards,
Bowen MFB Team"""
            }
        }

        if status in status_messages:
            message_data = status_messages[status]

            # Send SMS notification
            send_notification_async(
                notification_type='sms',
                recipient=customer.phone_number,
                message=message_data['sms']
            )

            # Send email notification
            send_notification_async(
                notification_type='email',
                recipient=customer.user.email,
                message=message_data['email_body'],
                subject=message_data['email_subject']
            )

            log_request(f"Sent {status} notification for single transfer {transfer_request.id} to {customer.user.email}")

            # Create in-app notification
            if transfer_request.company:
                notification_titles = {
                    'checked': 'Transfer Request Checked',
                    'verified': 'Transfer Request Verified',
                    'approved': 'Transfer Request Approved',
                    'declined': 'Transfer Request Declined'
                }

                notification_messages = {
                    'checked': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been checked.",
                    'verified': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been verified.",
                    'approved': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been approved.",
                    'declined': f"Your transfer request of ₦{transfer_request.amount:,.2f} to {transfer_request.beneficiary_name} has been declined."
                }

                create_in_app_notification(
                    company=transfer_request.company,
                    title=notification_titles.get(status, 'Transfer Status Update'),
                    message=notification_messages.get(status, f'Transfer status updated to {status}'),
                    notification_type=f'transfer_{status}',
                    related_object_id=transfer_request.id,
                    related_object_type='SingleTransferRequest'
                )

    except Exception as e:
        log_request(f"Failed to send single transfer {status} notification: {str(e)}")


@receiver(pre_save, sender=BulkTransferRequest)
def track_bulk_transfer_status_changes(sender, instance, **kwargs):
    """Track status changes for bulk transfer requests before saving."""
    if instance.pk:
        try:
            # Get the previous state from database
            old_instance = BulkTransferRequest.objects.get(pk=instance.pk)
            instance._old_is_checked = old_instance.is_checked
            instance._old_is_verified = old_instance.is_verified
            instance._old_is_approved = old_instance.is_approved
            instance._old_is_declined = old_instance.is_declined
        except BulkTransferRequest.DoesNotExist:
            instance._old_is_checked = False
            instance._old_is_verified = False
            instance._old_is_approved = False
            instance._old_is_declined = False
    else:
        instance._old_is_checked = False
        instance._old_is_verified = False
        instance._old_is_approved = False
        instance._old_is_declined = False


@receiver(post_save, sender=BulkTransferRequest)
def send_bulk_transfer_notifications(sender, instance, created, **kwargs):
    """Send notifications when bulk transfer request status changes."""
    if created:
        # Create in-app notification for new bulk transfer request
        if instance.company:
            transaction.on_commit(lambda: create_in_app_notification(
                company=instance.company,
                title="New Bulk Transfer Request Created",
                message=f"A new bulk transfer request of ₦{instance.total_amount:,.2f} has been created: {instance.description}",
                notification_type='transfer_created',
                related_object_id=instance.id,
                related_object_type='BulkTransferRequest'
            ))
        return

    # Check if any status has changed
    status_changes = []

    if hasattr(instance, '_old_is_checked') and not instance._old_is_checked and instance.is_checked:
        status_changes.append('checked')

    if hasattr(instance, '_old_is_verified') and not instance._old_is_verified and instance.is_verified:
        status_changes.append('verified')

    if hasattr(instance, '_old_is_approved') and not instance._old_is_approved and instance.is_approved:
        status_changes.append('approved')

    if hasattr(instance, '_old_is_declined') and not instance._old_is_declined and instance.is_declined:
        status_changes.append('declined')

    # Send notifications for each status change
    for status in status_changes:
        transaction.on_commit(lambda s=status: send_bulk_transfer_status_notification(instance, s))


def send_bulk_transfer_status_notification(transfer_request, status):
    """Send notification for bulk transfer status change."""
    try:
        # For bulk transfers, notify all active customers of the company
        if not transfer_request.company:
            return

        company_customers = Customer.objects.filter(company=transfer_request.company, active=True)
        company_name = transfer_request.company.name

        # Prepare status-specific messages
        status_messages = {
            'checked': {
                'sms': f"Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been checked and is under review.",
                'email_subject': 'Bulk Transfer Request Checked',
                'email_body': f"""Dear Team,

Your bulk transfer request has been checked and is now under review.

Transfer Details:
- Total Amount: ₦{transfer_request.total_amount:,.2f}
- Description: {transfer_request.description}
- Company: {company_name}

The request is now in the verification stage.

Best regards,
Bowen MFB Team"""
            },
            'verified': {
                'sms': f"Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been verified and is awaiting final approval.",
                'email_subject': 'Bulk Transfer Request Verified',
                'email_body': f"""Dear Team,

Your bulk transfer request has been verified and is awaiting final approval.

Transfer Details:
- Total Amount: ₦{transfer_request.total_amount:,.2f}
- Description: {transfer_request.description}
- Company: {company_name}

The request is now in the final approval stage.

Best regards,
Bowen MFB Team"""
            },
            'approved': {
                'sms': f"Great news! Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been approved and will be processed.",
                'email_subject': 'Bulk Transfer Request Approved',
                'email_body': f"""Dear Team,

Great news! Your bulk transfer request has been approved and will be processed shortly.

Transfer Details:
- Total Amount: ₦{transfer_request.total_amount:,.2f}
- Description: {transfer_request.description}
- Company: {company_name}

All transfers in this batch will be executed and you will receive confirmations once completed.

Best regards,
Bowen MFB Team"""
            },
            'declined': {
                'sms': f"Bulk transfer request for {company_name} (₦{transfer_request.total_amount:,.2f}) has been declined. Reason: {transfer_request.decline_reason or 'Not specified'}",
                'email_subject': 'Bulk Transfer Request Declined',
                'email_body': f"""Dear Team,

We regret to inform you that your bulk transfer request has been declined.

Transfer Details:
- Total Amount: ₦{transfer_request.total_amount:,.2f}
- Description: {transfer_request.description}
- Company: {company_name}

Reason for decline: {transfer_request.decline_reason or 'Not specified'}

Please contact your account officer for more information or to submit a new request.

Best regards,
Bowen MFB Team"""
            }
        }

        if status in status_messages:
            message_data = status_messages[status]

            # Send notifications to all company customers
            for customer in company_customers:
                # Send SMS notification
                send_notification_async(
                    notification_type='sms',
                    recipient=customer.phone_number,
                    message=message_data['sms']
                )

                # Send email notification
                send_notification_async(
                    notification_type='email',
                    recipient=customer.user.email,
                    message=message_data['email_body'],
                    subject=message_data['email_subject']
                )

            log_request(f"Sent {status} notification for bulk transfer {transfer_request.id} to {company_customers.count()} customers")

            # Create in-app notification
            notification_titles = {
                'checked': 'Bulk Transfer Request Checked',
                'verified': 'Bulk Transfer Request Verified',
                'approved': 'Bulk Transfer Request Approved',
                'declined': 'Bulk Transfer Request Declined'
            }

            notification_messages = {
                'checked': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been checked: {transfer_request.description}",
                'verified': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been verified: {transfer_request.description}",
                'approved': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been approved: {transfer_request.description}",
                'declined': f"Bulk transfer request of ₦{transfer_request.total_amount:,.2f} has been declined: {transfer_request.description}"
            }

            create_in_app_notification(
                company=transfer_request.company,
                title=notification_titles.get(status, 'Bulk Transfer Status Update'),
                message=notification_messages.get(status, f'Bulk transfer status updated to {status}'),
                notification_type=f'transfer_{status}',
                related_object_id=transfer_request.id,
                related_object_type='BulkTransferRequest'
            )

    except Exception as e:
        log_request(f"Failed to send bulk transfer {status} notification: {str(e)}")


