# Generated manually for signatory models

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0002_remove_companycreationrequest_account_opened_by_and_more'),
        ('transfer', '0002_bulktransferrequest_bulktransfer'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountSignatory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.CharField(choices=[('uploader', 'Uploader'), ('checker', 'Checker'), ('verifier', 'Verifier'), ('approver', 'Approver')], max_length=20)),
                ('hierarchy_level', models.IntegerField(choices=[(1, 'Level 1 - Uploader'), (2, 'Level 2 - Checker'), (3, 'Level 3 - Verifier'), (4, 'Level 4 - Approver'), (5, 'Level 5 - Final Approver')])),
                ('is_active', models.BooleanField(default=True)),
                ('can_upload', models.BooleanField(default=False)),
                ('can_check', models.BooleanField(default=False)),
                ('can_verify', models.BooleanField(default=False)),
                ('can_approve', models.BooleanField(default=False)),
                ('company_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signatories', to='account.companyaccount')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='signatory_accounts', to='account.customer')),
            ],
            options={
                'verbose_name': 'Account Signatory',
                'verbose_name_plural': 'Account Signatories',
            },
        ),
        migrations.CreateModel(
            name='SignatoryHierarchy',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_levels', models.IntegerField(default=1, help_text='Total number of approval levels (1-5)')),
                ('requires_checker', models.BooleanField(default=False)),
                ('requires_verifier', models.BooleanField(default=False)),
                ('requires_approver', models.BooleanField(default=True)),
                ('is_single_signatory', models.BooleanField(default=True, help_text='True if account is managed by single person')),
                ('company_account', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='hierarchy', to='account.companyaccount')),
            ],
            options={
                'verbose_name': 'Signatory Hierarchy',
                'verbose_name_plural': 'Signatory Hierarchies',
            },
        ),
        migrations.CreateModel(
            name='TransferApprovalWorkflow',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_level', models.IntegerField(default=1)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('approved', 'Approved'), ('declined', 'Declined'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('checked_at', models.DateTimeField(blank=True, null=True)),
                ('verified_at', models.DateTimeField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('declined_at', models.DateTimeField(blank=True, null=True)),
                ('decline_reason', models.TextField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to='account.customer')),
                ('bulk_transfer_request', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='approval_workflow', to='transfer.bulktransferrequest')),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='checked_transfers', to='account.customer')),
                ('declined_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='declined_transfers', to='account.customer')),
                ('single_transfer_request', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='approval_workflow', to='transfer.singletransferrequest')),
                ('uploaded_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_transfers', to='account.customer')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_transfers', to='account.customer')),
            ],
            options={
                'verbose_name': 'Transfer Approval Workflow',
                'verbose_name_plural': 'Transfer Approval Workflows',
            },
        ),
        migrations.AlterUniqueTogether(
            name='accountsignatory',
            unique_together={('company_account', 'customer')},
        ),
    ]
