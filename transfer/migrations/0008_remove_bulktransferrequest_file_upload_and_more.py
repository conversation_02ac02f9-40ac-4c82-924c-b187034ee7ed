# Generated by Django 5.2.1 on 2025-07-04 09:37

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('transfer', '0007_rename_provide_reference_bulktransfer_provider_reference_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='bulktransferrequest',
            name='file_upload',
        ),
        migrations.RemoveField(
            model_name='bulktransferrequest',
            name='from_account',
        ),
        migrations.RemoveField(
            model_name='bulktransferrequest',
            name='total_amount',
        ),
        migrations.RemoveField(
            model_name='bulktransferrequest',
            name='total_count',
        ),
        migrations.AddField(
            model_name='bulktransfer',
            name='nip_session_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='checked_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='created_on',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='declined_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='request_payload',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='scheduled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='scheduler',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transfer.transferscheduler'),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='updated_on',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='bulktransferrequest',
            name='verified_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
