# Generated by Django 5.2.1 on 2025-05-31 22:21

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0003_remove_companycreationrequest_approved_by_and_more'),
        ('transfer', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BulkTransferRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('total_amount', models.FloatField(default=0.0)),
                ('total_count', models.IntegerField(default=0)),
                ('description', models.CharField(max_length=200)),
                ('file_upload', models.FileField(blank=True, null=True, upload_to='bulk_transfers/')),
                ('is_checked', models.BooleanField(default=False)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_approved', models.BooleanField(default=False)),
                ('is_declined', models.BooleanField(default=False)),
                ('decline_reason', models.CharField(blank=True, max_length=250, null=True)),
                ('provider_response', models.TextField(blank=True, null=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bulk_transfer_approved_by', to='account.customer')),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bulk_transfer_checked_by', to='account.customer')),
                ('company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
                ('declined_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bulk_transfer_declined_by', to='account.customer')),
                ('from_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.companyaccount')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bulk_transfer_verified_by', to='account.customer')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BulkTransfer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('transfer_type', models.CharField(choices=[('intra', 'Local Transfer'), ('inter', 'Inter Bank Transfer')], default='intra', max_length=100)),
                ('beneficiary_name', models.CharField(max_length=200)),
                ('beneficiary_bank_name', models.CharField(max_length=200)),
                ('beneficiary_acct_number', models.CharField(max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('failed', 'Failed'), ('success', 'Success')], default='pending', max_length=100)),
                ('amount', models.FloatField(default=1.0)),
                ('fee', models.FloatField(default=0.0)),
                ('narration', models.TextField(blank=True, null=True)),
                ('reference', models.CharField(blank=True, max_length=12, null=True)),
                ('provide_reference', models.CharField(blank=True, max_length=200, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
                ('from_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.companyaccount')),
                ('bulk_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfers', to='transfer.bulktransferrequest')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
