# Generated by Django 5.2.1 on 2025-06-17 12:48

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('transfer', '0003_signatory_models'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='signatoryhierarchy',
            name='company_account',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='approved_by',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='bulk_transfer_request',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='checked_by',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='declined_by',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='single_transfer_request',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='uploaded_by',
        ),
        migrations.RemoveField(
            model_name='transferapprovalworkflow',
            name='verified_by',
        ),
        migrations.DeleteModel(
            name='AccountSignatory',
        ),
        migrations.DeleteModel(
            name='SignatoryHierarchy',
        ),
        migrations.DeleteModel(
            name='TransferApprovalWorkflow',
        ),
    ]
