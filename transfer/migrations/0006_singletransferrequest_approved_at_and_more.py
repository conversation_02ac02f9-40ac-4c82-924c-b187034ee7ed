# Generated by Django 5.2.1 on 2025-06-23 20:52

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0009_companycreationrequest_business_bvn'),
        ('transfer', '0005_signatoryhierarchy_transferapprovalworkflow_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='singletransferrequest',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='beneficiary_type',
            field=models.CharField(choices=[('intra', 'Local Transfer'), ('inter', 'Inter Bank Transfer')], default='intra', max_length=50),
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='checked_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='single_transfer_created_by', to='account.customer'),
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='created_on',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='declined_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='updated_on',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='singletransferrequest',
            name='verified_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
