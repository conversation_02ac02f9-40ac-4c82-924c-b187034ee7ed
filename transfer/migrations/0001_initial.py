# Generated by Django 5.2.1 on 2025-05-29 00:02

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BankList',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=300)),
                ('code', models.CharField(max_length=200)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransferScheduler',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('schedule_type', models.CharField(choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('bi-annually', 'Bi-annually'), ('yearly', 'Yearly'), ('once', 'One Time')], default='once', max_length=100)),
                ('day_of_the_month', models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5'), ('6', '6'), ('7', '7'), ('8', '8'), ('9', '9'), ('10', '10'), ('11', '11'), ('12', '12'), ('13', '13'), ('14', '14'), ('15', '15'), ('16', '16'), ('17', '17'), ('18', '18'), ('19', '19'), ('20', '20'), ('21', '21'), ('22', '22'), ('23', '23'), ('24', '24'), ('25', '25'), ('26', '26'), ('27', '27'), ('28', '28')], max_length=200, null=True)),
                ('day_of_the_week', models.CharField(blank=True, choices=[('1', 'Monday'), ('2', 'Tuesday'), ('3', 'Wednesday'), ('4', 'Thursday'), ('5', 'Friday'), ('6', 'Saturday'), ('7', 'Sunday')], max_length=100, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='inactive', max_length=50)),
                ('completed', models.BooleanField(default=False)),
                ('start_date', models.DateTimeField(null=True)),
                ('end_date', models.DateTimeField(null=True)),
                ('last_job_date', models.DateTimeField(blank=True, null=True)),
                ('next_job_date', models.DateTimeField(blank=True, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SingleTransferRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('amount', models.FloatField(default=1.0)),
                ('description', models.CharField(max_length=60)),
                ('beneficiary_account_number', models.CharField(max_length=20)),
                ('beneficiary_name', models.CharField(max_length=100)),
                ('beneficiary_bank_code', models.CharField(blank=True, max_length=20, null=True)),
                ('nip_session_id', models.CharField(blank=True, max_length=200, null=True)),
                ('bank_name', models.CharField(blank=True, max_length=100, null=True)),
                ('beneficiary_acct_type', models.CharField(blank=True, max_length=20, null=True)),
                ('scheduled', models.BooleanField(default=False)),
                ('decline_reason', models.CharField(blank=True, max_length=250, null=True)),
                ('provider_response', models.TextField(blank=True, null=True)),
                ('is_checked', models.BooleanField(default=False)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_approved', models.BooleanField(default=False)),
                ('is_declined', models.BooleanField(default=False)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='single_transfer_approved_by', to='account.customer')),
                ('checked_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='single_transfer_checked_by', to='account.customer')),
                ('company', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
                ('declined_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='single_transfer_declined_by', to='account.customer')),
                ('from_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.companyaccount')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='single_transfer_verified_by', to='account.customer')),
                ('scheduler', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transfer.transferscheduler')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SingleTransfer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('transfer_type', models.CharField(choices=[('intra', 'Local Transfer'), ('inter', 'Inter Bank Transfer')], default='intra', max_length=100)),
                ('beneficiary_name', models.CharField(max_length=200)),
                ('beneficiary_bank_name', models.CharField(max_length=200)),
                ('beneficiary_acct_number', models.CharField(max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('failed', 'Failed'), ('success', 'Success')], default='pending', max_length=100)),
                ('amount', models.FloatField(default=1.0)),
                ('fee', models.FloatField(default=0.0)),
                ('narration', models.TextField(blank=True, null=True)),
                ('reference', models.CharField(blank=True, max_length=12, null=True)),
                ('provide_reference', models.CharField(blank=True, max_length=200, null=True)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('updated_on', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.company')),
                ('from_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='account.companyaccount')),
                ('transfer_request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='transfer.singletransferrequest')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransferBeneficiary',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('beneficiary_type', models.CharField(choices=[('intra', 'Local Transfer'), ('inter', 'Inter Bank Transfer')], default='intra', max_length=200)),
                ('name', models.CharField(max_length=200)),
                ('bank_name', models.CharField(max_length=200)),
                ('bank_code', models.CharField(blank=True, max_length=200, null=True)),
                ('account_number', models.CharField(blank=True, max_length=200, null=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='account.company')),
            ],
            options={
                'verbose_name': 'Transfer Beneficiary',
                'verbose_name_plural': 'Transfer Beneficiaries',
            },
        ),
    ]
