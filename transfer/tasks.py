import json

from celery import shared_task
from django.conf import settings

from account.models import CompanyAccount
from bowenmfb.modules.utils import generate_transaction_reference, log_request
from transfer.models import TransferApprovalWorkflow, SingleTransfer, BulkTransfer, BulkTransferRequest
from bowenmfb.modules.bankone import BankOneClient

client = BankOneClient()


@shared_task
def perform_bowen_fund_transfer(workflow_id):
    try:
        workflow = TransferApprovalWorkflow.objects.get(id=workflow_id, status="approved")
    except TransferApprovalWorkflow.DoesNotExist:
        return False

    if workflow.single_transfer_request:
        transfer_request = workflow.single_transfer_request
    else:
        transfer_request = workflow.bulk_transfer_request

    if SingleTransfer.objects.filter(transfer_request_id=transfer_request.id).exists() or \
            BulkTransfer.objects.filter(bulk_request_id=transfer_request.id).exists():
        workflow.process_response = "Transfer already exist/completed"
        workflow.save()
        return False

    if workflow.status != "approved":
        workflow.process_response = "Workflow has already been approved"
        workflow.save()
        return False

    # Create transaction request
    ref_number = generate_transaction_reference()
    if workflow.single_transfer_request:
        single_request = workflow.single_transfer_request
        company = single_request.company
        beneficiary_type = single_request.beneficiary_type
        sending_account = single_request.from_account
        amount = single_request.amount
        receiver_account_number = single_request.beneficiary_account_number
        receiver_name = single_request.beneficiary_name

        company_name_short = str(company.name)[:10]
        narration_short = str(single_request.description)[:10]

        description = f"BOWENMFB/{company_name_short}/{narration_short}"
        transfer = SingleTransfer.objects.create(
            company=company, transfer_request=single_request, from_account_id=sending_account.id,
            transfer_type=beneficiary_type, beneficiary_name=receiver_name, beneficiary_bank_name=single_request.bank_name,
            beneficiary_acct_number=receiver_account_number, amount=amount, narration=single_request.description,
            reference=ref_number
        )
        # Call Core Banking to Send Fund
        sender_account_number = sending_account.account_number

        if beneficiary_type == "intra":
            intra_response = client.local_funds_transfer(
                from_account_number=sender_account_number, amount=amount, to_account_number=receiver_account_number,
                retrieval_reference=ref_number, narration=description
            )
            if ("IsSuccessful" and "ResponseCode" in intra_response) and \
                    (intra_response["IsSuccessful"] is True and intra_response["ResponseCode"] == "00"):
                # Transfer successful
                transfer.status = "success"
                transfer.provider_reference = intra_response["Reference"]
            else:
                transfer.status = "failed"
            transfer.save()
            single_request.provider_response = intra_response
            single_request.save()

        elif beneficiary_type == "inter":
            bank_code = single_request.beneficiary_bank_code
            bank_account_identifier = sending_account.bank_one_account_number
            nip_session = single_request.nip_session_id
            if settings.DEBUG is True:
                inter_response = {
                                    "Status": "Successful",
                                    "StatusDescription": None,
                                    "ReferenceID": 0,
                                    "UniqueIdentifier": "020067152410012328260000000000000000000000",
                                    "IsSuccessFul": True,
                                    "ResponseMessage": None,
                                    "ResponseCode": "00",
                                    "RequestStatus": True,
                                    "ResponseDescription": None,
                                    "ResponseStatus": "Successful"
                                }
            else:
                inter_response = client.inter_bank_transfer(
                    amount=amount, payer=str(company.name).upper(), payer_account_number=sender_account_number,
                    receiver_account_number=receiver_account_number, receiver_bank_code=bank_code, narration=description,
                    transaction_reference=ref_number, receiver_name=receiver_name, nip_session_id=nip_session, appzone_account=bank_account_identifier
                )

            if ("Status" in inter_response and inter_response["Status"] in ["SuccessfulButFeeNotTaken", "SuccesfulButFeeNotTaken", "Successful"])\
                    and ("ResponseCode" in inter_response and inter_response["ResponseCode"] == "00"):
                # Transfer Successful
                transfer.status = "success"
                transfer.provider_reference = inter_response["UniqueIdentifier"]
            elif ("Status" in inter_response and inter_response["Status"] == "Pending") or \
                    ("ResponseCode" in inter_response and inter_response["ResponseCode"] in ["91", "06"]):
                # Transaction Status Query is required
                ...
            else:
                transfer.status = "failed"
            transfer.save()
            single_request.provider_response = inter_response
            single_request.save()

        else:
            workflow.process_response = "Invalid transfer or beneficiary type"
            workflow.save()
            return False

    if workflow.bulk_transfer_request:
        ...

    return True

@shared_task()
def create_bulk_transfer(bulk_trans_id, scheduled):
    total_amount = 0

    try:
        bulk_trans = BulkTransferRequest.objects.get(id=bulk_trans_id)
    except BulkTransferRequest.DoesNotExist:
        log_request(f"Bulk Transfer Request: {bulk_trans_id} not found")
        return False

    data = json.loads(bulk_trans.request_payload)

    for item in data:
        account_no = item["from_account_number"]
        amount = item["amount"]
        narration = item["narration"]
        ben_name = item["beneficiary_name"]
        trans_type = item["beneficiary_type"]
        ben_acct_no = item["beneficiary_account_number"]
        bank_code = item["beneficiary_bank_code"] if "beneficiary_bank_code" in item else ""
        nip_id = item["nip_session_id"] if "nip_session_id" in item else ""
        bank_name = item["beneficiary_bank_name"] if "beneficiary_bank_name" in item else ""
        total_amount += amount

        comp_acct = CompanyAccount.objects.filter(account_number=account_no, company=bulk_trans.company)
        if not comp_acct.exists():
            pass
        else:
            acct = comp_acct.last()
            # Create Bulk Transfers
            company_name = acct.company.name
            company_name_short = str(company_name)[:10]
            narration_short = str(narration)[:10]

            description = f"BOWENMFB/{company_name_short}/{narration_short}"

            ref_number = generate_transaction_reference()
            bt = BulkTransfer.objects.create(
                bulk_request=bulk_trans, company=bulk_trans.company, from_account=acct, transfer_type=trans_type, beneficiary_name=ben_name,
                beneficiary_bank_name=bank_name, beneficiary_acct_number=ben_acct_no, amount=amount, narration=narration, nip_session_id=nip_id,
                reference=ref_number, beneficiary_bank_code=bank_code
            )

            # Send the fund if not scheduled
            if scheduled == "false":
                sending_account_number = acct.account_number
                if trans_type == "intra":
                    intra_response = client.local_funds_transfer(
                        from_account_number=sending_account_number, amount=amount, to_account_number=ben_acct_no,
                        retrieval_reference=ref_number, narration=description
                    )
                    if ("IsSuccessful" and "ResponseCode" in intra_response) and \
                            (intra_response["IsSuccessful"] is True and intra_response["ResponseCode"] == "00"):
                        # Transfer successful
                        bt.status = "success"
                        bt.provider_reference = intra_response["Reference"]
                    else:
                        bt.status = "failed"
                    bt.save()
                    bt.provider_response = intra_response
                    bt.save()

                elif trans_type == "inter":
                    bank_code = bank_code
                    bank_account_identifier = acct.bank_one_account_number
                    nip_session = nip_id
                    if settings.DEBUG:
                        inter_response = {
                            "Status": "Successful",
                            "StatusDescription": None,
                            "ReferenceID": 0,
                            "UniqueIdentifier": "020067152410012328260000000000000000000000",
                            "IsSuccessFul": True,
                            "ResponseMessage": None,
                            "ResponseCode": "00",
                            "RequestStatus": True,
                            "ResponseDescription": None,
                            "ResponseStatus": "Successful"
                        }
                    else:
                        inter_response = client.inter_bank_transfer(
                            amount=amount, payer=str(company_name).upper(), payer_account_number=sending_account_number,
                            receiver_account_number=ben_acct_no, receiver_bank_code=bank_code, narration=description,
                            transaction_reference=ref_number, receiver_name=ben_name, nip_session_id=nip_session,
                            appzone_account=bank_account_identifier
                        )

                    if ("Status" in inter_response and inter_response["Status"] in ["SuccessfulButFeeNotTaken", "SuccesfulButFeeNotTaken",
                                                                                    "Successful"]) \
                            and ("ResponseCode" in inter_response and inter_response["ResponseCode"] == "00"):
                        # Transfer Successful
                        bt.status = "success"
                        bt.provider_reference = inter_response["UniqueIdentifier"]
                    elif ("Status" in inter_response and inter_response["Status"] == "Pending") or \
                            ("ResponseCode" in inter_response and inter_response["ResponseCode"] in ["91", "06"]):
                        # Transaction Status Query is required
                        ...
                    else:
                        bt.status = "failed"
                    bt.provider_response = inter_response
                    bt.save()

    bulk_trans.total_amount = total_amount
    bulk_trans.save()


    return True

